.BiddingModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-md);
}

.BiddingModal__content {
  background-color: var(--bg-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  border: 1px solid var(--border-light);
}

.BiddingModal__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
}

.BiddingModal__title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.BiddingModal__closeButton {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.BiddingModal__closeButton:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.BiddingModal__productInfo {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
}

.BiddingModal__productImage {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--radius-md);
  flex-shrink: 0;
}

.BiddingModal__productDetails {
  flex: 1;
}

.BiddingModal__productTitle {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1.3;
}

.BiddingModal__productPrice {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.BiddingModal__productPrice span {
  font-weight: 600;
  color: var(--primary-color);
}

.BiddingModal__highestBid {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.BiddingModal__highestBid span {
  font-weight: 600;
  color: var(--success-color);
}

.BiddingModal__form {
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.BiddingModal__field {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.BiddingModal__label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.BiddingModal__icon {
  font-size: var(--font-size-lg);
  color: var(--primary-color);
}

.BiddingModal__input {
  padding: var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-lg);
  font-weight: 600;
  text-align: center;
  transition: all var(--transition-fast);
}

.BiddingModal__input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.BiddingModal__input--error {
  border-color: var(--danger-color);
}

.BiddingModal__error {
  font-size: var(--font-size-sm);
  color: var(--danger-color);
  text-align: center;
}

.BiddingModal__suggestions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.BiddingModal__suggestionsLabel {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-secondary);
}

.BiddingModal__suggestedBids {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: var(--spacing-sm);
}

.BiddingModal__suggestedBid {
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.BiddingModal__suggestedBid:hover {
  background-color: var(--primary-color);
  color: var(--text-white);
  border-color: var(--primary-color);
}

.BiddingModal__info {
  background-color: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.BiddingModal__infoItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.BiddingModal__infoLabel {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.BiddingModal__infoValue {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.BiddingModal__actions {
  display: flex;
  gap: var(--spacing-md);
}

.BiddingModal__cancelButton {
  flex: 1;
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.BiddingModal__cancelButton:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.BiddingModal__submitButton {
  flex: 2;
  padding: var(--spacing-md);
  background-color: var(--primary-color);
  border: none;
  border-radius: var(--radius-lg);
  color: var(--text-white);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.BiddingModal__submitButton:hover:not(:disabled) {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.BiddingModal__submitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.BiddingModal__disclaimer {
  padding: var(--spacing-md) var(--spacing-xl);
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-light);
}

.BiddingModal__disclaimer p {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  line-height: 1.5;
  margin: 0;
}

/* Mobile Styles */
@media (max-width: 480px) {
  .BiddingModal {
    padding: var(--spacing-sm);
  }

  .BiddingModal__content {
    max-height: 95vh;
  }

  .BiddingModal__header,
  .BiddingModal__form,
  .BiddingModal__disclaimer {
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
  }

  .BiddingModal__productInfo {
    padding: var(--spacing-lg);
  }

  .BiddingModal__suggestedBids {
    grid-template-columns: repeat(2, 1fr);
  }

  .BiddingModal__actions {
    flex-direction: column;
  }
}
