import React, { createContext, useContext, useState, useEffect } from 'react';
import { 
  products as initialProducts, 
  categories, 
  sports,
  getFeaturedProducts,
  getProductsByCategory,
  getProductsBySport,
  getProductById 
} from '../data/mockData';

const ProductContext = createContext();

export const useProducts = () => {
  const context = useContext(ProductContext);
  if (!context) {
    throw new Error('useProducts must be used within a ProductProvider');
  }
  return context;
};

export const ProductProvider = ({ children }) => {
  const [products, setProducts] = useState(initialProducts);
  const [filteredProducts, setFilteredProducts] = useState(initialProducts);
  const [filters, setFilters] = useState({
    category: 'all',
    sport: 'all',
    priceRange: [0, 200],
    level: 'all',
    type: 'all',
    searchQuery: ''
  });
  const [sortBy, setSortBy] = useState('featured'); // featured, price-low, price-high, rating, newest
  const [isLoading, setIsLoading] = useState(false);

  // Apply filters and sorting whenever filters or sortBy changes
  useEffect(() => {
    applyFiltersAndSort();
  }, [filters, sortBy, products]);

  const applyFiltersAndSort = () => {
    let filtered = [...products];

    // Apply category filter
    if (filters.category !== 'all') {
      filtered = filtered.filter(product => product.category === filters.category);
    }

    // Apply sport filter
    if (filters.sport !== 'all') {
      filtered = filtered.filter(product => product.sport === filters.sport);
    }

    // Apply price range filter
    filtered = filtered.filter(product => 
      product.price >= filters.priceRange[0] && product.price <= filters.priceRange[1]
    );

    // Apply level filter
    if (filters.level !== 'all') {
      filtered = filtered.filter(product => 
        product.level.toLowerCase() === filters.level.toLowerCase()
      );
    }

    // Apply type filter
    if (filters.type !== 'all') {
      filtered = filtered.filter(product => product.type === filters.type);
    }

    // Apply search query
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filtered = filtered.filter(product =>
        product.title.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query) ||
        product.tags.some(tag => tag.toLowerCase().includes(query)) ||
        product.seller.name.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'newest':
        filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        break;
      case 'featured':
      default:
        filtered.sort((a, b) => {
          if (a.featured && !b.featured) return -1;
          if (!a.featured && b.featured) return 1;
          return b.rating - a.rating;
        });
        break;
    }

    setFilteredProducts(filtered);
  };

  const updateFilters = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const resetFilters = () => {
    setFilters({
      category: 'all',
      sport: 'all',
      priceRange: [0, 200],
      level: 'all',
      type: 'all',
      searchQuery: ''
    });
  };

  const addProduct = async (productData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newProduct = {
        id: products.length + 1,
        ...productData,
        rating: 0,
        reviews: 0,
        createdAt: new Date().toISOString().split('T')[0],
        bids: [],
        featured: false
      };
      
      setProducts(prev => [...prev, newProduct]);
      return { success: true, product: newProduct };
    } catch (error) {
      return { success: false, error: 'Failed to add product' };
    } finally {
      setIsLoading(false);
    }
  };

  const updateProduct = async (productId, updates) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setProducts(prev => 
        prev.map(product => 
          product.id === productId ? { ...product, ...updates } : product
        )
      );
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to update product' };
    } finally {
      setIsLoading(false);
    }
  };

  const deleteProduct = async (productId) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setProducts(prev => prev.filter(product => product.id !== productId));
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to delete product' };
    } finally {
      setIsLoading(false);
    }
  };

  const addBid = async (productId, bidData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newBid = {
        id: Date.now(),
        ...bidData,
        timestamp: new Date().toISOString()
      };
      
      setProducts(prev => 
        prev.map(product => 
          product.id === productId 
            ? { ...product, bids: [...product.bids, newBid] }
            : product
        )
      );
      return { success: true, bid: newBid };
    } catch (error) {
      return { success: false, error: 'Failed to place bid' };
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    products,
    filteredProducts,
    filters,
    sortBy,
    isLoading,
    categories,
    sports,
    updateFilters,
    resetFilters,
    setSortBy,
    addProduct,
    updateProduct,
    deleteProduct,
    addBid,
    getFeaturedProducts: () => getFeaturedProducts(),
    getProductsByCategory: (category) => getProductsByCategory(category),
    getProductsBySport: (sport) => getProductsBySport(sport),
    getProductById: (id) => getProductById(id)
  };

  return (
    <ProductContext.Provider value={value}>
      {children}
    </ProductContext.Provider>
  );
};
