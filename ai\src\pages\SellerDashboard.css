.SellerDashboard {
  padding: var(--spacing-2xl) 0;
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

.SellerDashboard__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-2xl);
  gap: var(--spacing-md);
}

.SellerDashboard__headerContent {
  flex: 1;
}

.SellerDashboard__title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.SellerDashboard__subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
}

.SellerDashboard__addButton {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
}

.SellerDashboard__addButton:hover {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.SellerDashboard__addButton--small {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-sm);
}

/* Stats */
.SellerDashboard__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.SellerDashboard__stat {
  background-color: var(--bg-card);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  transition: transform var(--transition-fast);
}

.SellerDashboard__stat:hover {
  transform: translateY(-2px);
}

.SellerDashboard__statIcon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-2xl);
  color: var(--text-white);
}

.SellerDashboard__stat--success .SellerDashboard__statIcon {
  background-color: var(--success-color);
}

.SellerDashboard__stat--primary .SellerDashboard__statIcon {
  background-color: var(--primary-color);
}

.SellerDashboard__stat--warning .SellerDashboard__statIcon {
  background-color: var(--warning-color);
}

.SellerDashboard__stat--info .SellerDashboard__statIcon {
  background-color: var(--secondary-color);
}

.SellerDashboard__statContent {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.SellerDashboard__statValue {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
}

.SellerDashboard__statLabel {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

/* Tabs */
.SellerDashboard__tabs {
  display: flex;
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xs);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.SellerDashboard__tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: none;
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.SellerDashboard__tab:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.SellerDashboard__tab--active {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.SellerDashboard__tab--active:hover {
  background-color: var(--primary-hover);
}

.SellerDashboard__tabIcon {
  font-size: var(--font-size-lg);
}

/* Content */
.SellerDashboard__content {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.SellerDashboard__section {
  margin-bottom: var(--spacing-2xl);
}

.SellerDashboard__section:last-child {
  margin-bottom: 0;
}

.SellerDashboard__sectionTitle {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

.SellerDashboard__sectionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
}

/* Overview */
.SellerDashboard__overview {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.SellerDashboard__activity {
  background-color: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.SellerDashboard__noActivity {
  text-align: center;
  color: var(--text-muted);
  font-style: italic;
}

.SellerDashboard__quickActions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.SellerDashboard__quickAction {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.SellerDashboard__quickAction:hover {
  background-color: var(--primary-color);
  color: var(--text-white);
  transform: translateY(-2px);
}

.SellerDashboard__quickActionIcon {
  font-size: var(--font-size-2xl);
}

/* Products */
.SellerDashboard__productsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.SellerDashboard__productCard {
  position: relative;
}

.SellerDashboard__productActions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.SellerDashboard__productAction {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.SellerDashboard__productAction--edit {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.SellerDashboard__productAction--edit:hover {
  background-color: var(--primary-color);
  color: var(--text-white);
  border-color: var(--primary-color);
}

.SellerDashboard__productAction--delete {
  background-color: var(--bg-secondary);
  color: var(--danger-color);
}

.SellerDashboard__productAction--delete:hover {
  background-color: var(--danger-color);
  color: var(--text-white);
  border-color: var(--danger-color);
}

/* Empty State */
.SellerDashboard__emptyState {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.SellerDashboard__emptyIcon {
  font-size: 4rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-lg);
}

.SellerDashboard__emptyState h3 {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.SellerDashboard__emptyState p {
  margin-bottom: var(--spacing-lg);
}

/* Bids */
.SellerDashboard__bidsList {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.SellerDashboard__productBids {
  background-color: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.SellerDashboard__productTitle {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.SellerDashboard__bidsContainer {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.SellerDashboard__bid {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.SellerDashboard__bidInfo {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.SellerDashboard__bidder {
  font-weight: 500;
  color: var(--text-primary);
}

.SellerDashboard__bidAmount {
  font-weight: 600;
  color: var(--success-color);
  font-size: var(--font-size-lg);
}

.SellerDashboard__bidTime {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.SellerDashboard__bidActions {
  display: flex;
  gap: var(--spacing-sm);
}

.SellerDashboard__bidAction {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.SellerDashboard__bidAction--accept {
  background-color: var(--success-color);
  color: var(--text-white);
}

.SellerDashboard__bidAction--accept:hover {
  background-color: #059669;
  transform: translateY(-1px);
}

.SellerDashboard__bidAction--decline {
  background-color: var(--danger-color);
  color: var(--text-white);
}

.SellerDashboard__bidAction--decline:hover {
  background-color: #dc2626;
  transform: translateY(-1px);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .SellerDashboard__header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-lg);
  }

  .SellerDashboard__title {
    font-size: var(--font-size-2xl);
  }

  .SellerDashboard__tabs {
    flex-direction: column;
  }

  .SellerDashboard__tab {
    justify-content: flex-start;
  }

  .SellerDashboard__productsGrid {
    grid-template-columns: 1fr;
  }

  .SellerDashboard__productActions {
    flex-direction: column;
  }

  .SellerDashboard__bid {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .SellerDashboard__bidInfo {
    justify-content: space-between;
  }

  .SellerDashboard__bidActions {
    justify-content: center;
  }
}
