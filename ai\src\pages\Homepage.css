.Homepage {
  min-height: 100vh;
}

/* Hero Section */
.Homepage__hero {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  padding: var(--spacing-2xl) 0;
}

.Homepage__heroContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  align-items: center;
  margin-bottom: var(--spacing-2xl);
}

.Homepage__heroText {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.Homepage__heroTitle {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
  margin: 0;
}

.Homepage__heroSubtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.Homepage__heroSearch {
  display: flex;
  gap: var(--spacing-sm);
  max-width: 500px;
}

.Homepage__searchInput {
  position: relative;
  flex: 1;
}

.Homepage__searchInput input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg) var(--spacing-md) 3rem;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-base);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-fast);
}

.Homepage__searchInput input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1), var(--shadow-lg);
}

.Homepage__searchIcon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: var(--font-size-xl);
}

.Homepage__searchButton {
  padding: var(--spacing-md) var(--spacing-xl);
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-xl);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
}

.Homepage__searchButton:hover {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.Homepage__popularSearches {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--spacing-sm);
}

.Homepage__popularLabel {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  font-weight: 500;
}

.Homepage__popularTags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.Homepage__popularTag {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.Homepage__popularTag:hover {
  background-color: var(--primary-color);
  color: var(--text-white);
  border-color: var(--primary-color);
}

.Homepage__heroImage {
  display: flex;
  justify-content: center;
  align-items: center;
}

.Homepage__heroImg {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
}

.Homepage__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-2xl);
}

.Homepage__stat {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background-color: var(--bg-card);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.Homepage__statIcon {
  font-size: var(--font-size-3xl);
  color: var(--primary-color);
}

.Homepage__statContent {
  display: flex;
  flex-direction: column;
}

.Homepage__statValue {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
}

.Homepage__statLabel {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* Section Styles */
.Homepage__featured,
.Homepage__browse {
  padding: var(--spacing-2xl) 0;
}

.Homepage__browse {
  background-color: var(--bg-secondary);
}

.Homepage__sectionHeader {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.Homepage__sectionTitle {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.Homepage__sectionSubtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.Homepage__featuredGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.Homepage__sectionFooter {
  text-align: center;
}

.Homepage__viewAllButton {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-xl);
  background-color: var(--primary-color);
  color: var(--text-white);
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
}

.Homepage__viewAllButton:hover {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Browse Section */
.Homepage__browseContent {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: var(--spacing-2xl);
}

.Homepage__filters {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.Homepage__filterSection {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-light);
}

.Homepage__filterTitle {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.Homepage__filterSelect {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background-color: var(--bg-card);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.Homepage__productsSection {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.Homepage__sortControls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
}

.Homepage__resultsCount {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.Homepage__sortSelect {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background-color: var(--bg-card);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.Homepage__productsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.Homepage__noResults {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .Homepage__heroContent {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .Homepage__heroTitle {
    font-size: var(--font-size-3xl);
  }

  .Homepage__heroSearch {
    flex-direction: column;
  }

  .Homepage__browseContent {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .Homepage__filters {
    order: 2;
  }

  .Homepage__productsSection {
    order: 1;
  }

  .Homepage__featuredGrid,
  .Homepage__productsGrid {
    grid-template-columns: 1fr;
  }

  .Homepage__sortControls {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }
}
