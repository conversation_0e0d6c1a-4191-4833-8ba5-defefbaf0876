.Bidding {
  padding: var(--spacing-2xl) 0;
  min-height: 100vh;
}

.Bidding__header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.Bidding__title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.Bidding__subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Stats */
.Bidding__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.Bidding__stat {
  background-color: var(--bg-card);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  transition: transform var(--transition-fast);
}

.Bidding__stat:hover {
  transform: translateY(-2px);
}

.Bidding__statIcon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-2xl);
  color: var(--text-white);
}

.Bidding__stat--primary .Bidding__statIcon {
  background-color: var(--primary-color);
}

.Bidding__stat--success .Bidding__statIcon {
  background-color: var(--success-color);
}

.Bidding__stat--warning .Bidding__statIcon {
  background-color: var(--warning-color);
}

.Bidding__statContent {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.Bidding__statValue {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
}

.Bidding__statLabel {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

/* Controls */
.Bidding__controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-2xl);
  padding: var(--spacing-lg);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.Bidding__filters {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.Bidding__filterIcon {
  color: var(--text-muted);
  font-size: var(--font-size-lg);
}

.Bidding__filterSelect,
.Bidding__sortSelect {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background-color: var(--bg-card);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  cursor: pointer;
}

.Bidding__sorting {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.Bidding__sortLabel {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

/* How It Works */
.Bidding__howItWorks {
  background-color: var(--bg-secondary);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  margin-bottom: var(--spacing-2xl);
}

.Bidding__sectionTitle {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.Bidding__steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.Bidding__step {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  background-color: var(--bg-card);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.Bidding__stepNumber {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: var(--text-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.Bidding__stepContent h3 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.Bidding__stepContent p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Products */
.Bidding__products {
  margin-bottom: var(--spacing-2xl);
}

.Bidding__productsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

/* Empty State */
.Bidding__emptyState {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.Bidding__emptyIcon {
  font-size: 4rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-lg);
}

.Bidding__emptyState h3 {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.Bidding__emptyState p {
  margin-bottom: var(--spacing-lg);
}

/* Tips */
.Bidding__tips {
  background-color: var(--bg-secondary);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
}

.Bidding__tipsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.Bidding__tip {
  background-color: var(--bg-card);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.Bidding__tip h4 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.Bidding__tip p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .Bidding__controls {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .Bidding__filters,
  .Bidding__sorting {
    justify-content: space-between;
  }

  .Bidding__steps {
    grid-template-columns: 1fr;
  }

  .Bidding__step {
    flex-direction: column;
    text-align: center;
  }

  .Bidding__productsGrid {
    grid-template-columns: 1fr;
  }

  .Bidding__tipsGrid {
    grid-template-columns: 1fr;
  }
}
