import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import { ProductProvider } from './context/ProductContext';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import Homepage from './pages/Homepage';
import ProductDetail from './pages/ProductDetail';
import Login from './pages/Login';
import SellerDashboard from './pages/SellerDashboard';
import Bidding from './pages/Bidding';
import './App.css';

function App() {
  return (
    <AuthProvider>
      <ProductProvider>
        <Router>
          <div className="App">
            <Navbar />
            <main className="App__main">
              <Routes>
                <Route path="/" element={<Homepage />} />
                <Route path="/product/:id" element={<ProductDetail />} />
                <Route path="/login" element={<Login />} />
                <Route path="/seller-dashboard" element={<SellerDashboard />} />
                <Route path="/bidding" element={<Bidding />} />
              </Routes>
            </main>
            <Footer />
          </div>
        </Router>
      </ProductProvider>
    </AuthProvider>
  );
}

export default App;
