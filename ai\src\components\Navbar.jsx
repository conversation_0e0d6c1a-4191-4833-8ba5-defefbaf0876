import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FiSearch, FiUser, FiShoppingCart, FiMenu, FiX, FiLogOut } from 'react-icons/fi';
import { useAuth } from '../context/AuthContext';
import { useProducts } from '../context/ProductContext';
import './Navbar.css';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { user, logout, isAuthenticated, isSeller } = useAuth();
  const { updateFilters } = useProducts();
  const navigate = useNavigate();

  const handleSearch = (e) => {
    e.preventDefault();
    updateFilters({ searchQuery });
    navigate('/');
    setIsMenuOpen(false);
  };

  const handleLogout = () => {
    logout();
    setIsProfileOpen(false);
    navigate('/');
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleProfile = () => {
    setIsProfileOpen(!isProfileOpen);
  };

  return (
    <nav className="Navbar">
      <div className="container">
        <div className="Navbar__content">
          {/* Logo */}
          <Link to="/" className="Navbar__logo">
            <span className="Navbar__logoText">SportsHub</span>
          </Link>

          {/* Search Bar - Desktop */}
          <form className="Navbar__search" onSubmit={handleSearch}>
            <div className="Navbar__searchInput">
              <FiSearch className="Navbar__searchIcon" />
              <input
                type="text"
                placeholder="Search for training videos, eBooks, plans..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <button type="submit" className="Navbar__searchButton">
              Search
            </button>
          </form>

          {/* Navigation Links - Desktop */}
          <div className="Navbar__links">
            <Link to="/" className="Navbar__link">
              Browse
            </Link>
            {isSeller && (
              <Link to="/seller-dashboard" className="Navbar__link">
                Dashboard
              </Link>
            )}
            <Link to="/bidding" className="Navbar__link">
              Bidding
            </Link>
          </div>

          {/* User Actions */}
          <div className="Navbar__actions">
            {isAuthenticated ? (
              <div className="Navbar__userMenu">
                <button 
                  className="Navbar__userButton"
                  onClick={toggleProfile}
                >
                  <img 
                    src={user.avatar} 
                    alt={user.name}
                    className="Navbar__userAvatar"
                  />
                  <span className="Navbar__userName">{user.name}</span>
                </button>
                
                {isProfileOpen && (
                  <div className="Navbar__dropdown">
                    <Link 
                      to="/profile" 
                      className="Navbar__dropdownItem"
                      onClick={() => setIsProfileOpen(false)}
                    >
                      <FiUser />
                      Profile
                    </Link>
                    {isSeller && (
                      <Link 
                        to="/seller-dashboard" 
                        className="Navbar__dropdownItem"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <FiShoppingCart />
                        Dashboard
                      </Link>
                    )}
                    <button 
                      className="Navbar__dropdownItem"
                      onClick={handleLogout}
                    >
                      <FiLogOut />
                      Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="Navbar__authButtons">
                <Link to="/login" className="Navbar__loginButton">
                  Login
                </Link>
                <Link to="/login?mode=register" className="Navbar__registerButton">
                  Sign Up
                </Link>
              </div>
            )}

            {/* Mobile Menu Button */}
            <button 
              className="Navbar__mobileMenuButton"
              onClick={toggleMenu}
            >
              {isMenuOpen ? <FiX /> : <FiMenu />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="Navbar__mobileMenu">
            {/* Mobile Search */}
            <form className="Navbar__mobileSearch" onSubmit={handleSearch}>
              <div className="Navbar__searchInput">
                <FiSearch className="Navbar__searchIcon" />
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <button type="submit" className="Navbar__searchButton">
                Search
              </button>
            </form>

            {/* Mobile Navigation Links */}
            <div className="Navbar__mobileLinks">
              <Link 
                to="/" 
                className="Navbar__mobileLink"
                onClick={() => setIsMenuOpen(false)}
              >
                Browse
              </Link>
              {isSeller && (
                <Link 
                  to="/seller-dashboard" 
                  className="Navbar__mobileLink"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Dashboard
                </Link>
              )}
              <Link 
                to="/bidding" 
                className="Navbar__mobileLink"
                onClick={() => setIsMenuOpen(false)}
              >
                Bidding
              </Link>
              
              {!isAuthenticated && (
                <>
                  <Link 
                    to="/login" 
                    className="Navbar__mobileLink"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Login
                  </Link>
                  <Link 
                    to="/login?mode=register" 
                    className="Navbar__mobileLink"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sign Up
                  </Link>
                </>
              )}
              
              {isAuthenticated && (
                <>
                  <Link 
                    to="/profile" 
                    className="Navbar__mobileLink"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Profile
                  </Link>
                  <button 
                    className="Navbar__mobileLink"
                    onClick={handleLogout}
                  >
                    Logout
                  </button>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
