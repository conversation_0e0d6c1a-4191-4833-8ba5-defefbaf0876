.ProductDetail {
  padding: var(--spacing-2xl) 0;
  min-height: 100vh;
}

.ProductDetail__loading,
.ProductDetail__notFound {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.ProductDetail__loadingContent,
.ProductDetail__notFoundContent {
  text-align: center;
  color: var(--text-secondary);
}

.ProductDetail__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  align-items: start;
}

/* Media Section */
.ProductDetail__media {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.ProductDetail__imageContainer {
  position: relative;
  width: 100%;
  aspect-ratio: 4/3;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.ProductDetail__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ProductDetail__overlay {
  position: absolute;
  top: var(--spacing-md);
  left: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background-color: rgba(0, 0, 0, 0.7);
  color: var(--text-white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.ProductDetail__typeIcon {
  font-size: var(--font-size-lg);
}

.ProductDetail__discount {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background-color: var(--danger-color);
  color: var(--text-white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.ProductDetail__thumbnails {
  display: flex;
  gap: var(--spacing-sm);
  overflow-x: auto;
}

.ProductDetail__thumbnail {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--radius-md);
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color var(--transition-fast);
}

.ProductDetail__thumbnail:hover {
  border-color: var(--primary-color);
}

/* Product Info */
.ProductDetail__info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.ProductDetail__header {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.ProductDetail__meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.ProductDetail__level {
  background-color: var(--primary-color);
  color: var(--text-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.ProductDetail__separator {
  color: var(--text-muted);
}

.ProductDetail__duration {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.ProductDetail__metaIcon {
  font-size: var(--font-size-sm);
}

.ProductDetail__title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
  margin: 0;
}

.ProductDetail__rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.ProductDetail__stars {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.ProductDetail__starIcon {
  color: var(--accent-color);
  font-size: var(--font-size-lg);
}

.ProductDetail__ratingValue {
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
}

.ProductDetail__reviews {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.ProductDetail__description h3,
.ProductDetail__tags h4,
.ProductDetail__seller h4,
.ProductDetail__bidding h4 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.ProductDetail__description p {
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: var(--font-size-base);
}

.ProductDetail__tagList {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.ProductDetail__tag {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* Seller Section */
.ProductDetail__sellerCard {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background-color: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.ProductDetail__sellerAvatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.ProductDetail__sellerInfo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.ProductDetail__sellerName {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.ProductDetail__sellerRating {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.ProductDetail__sellerStarIcon {
  color: var(--accent-color);
  font-size: var(--font-size-sm);
}

.ProductDetail__sellerSales {
  color: var(--text-muted);
}

/* Bidding Section */
.ProductDetail__bidInfo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--bg-tertiary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  margin-bottom: var(--spacing-md);
}

.ProductDetail__highestBid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.ProductDetail__bidLabel {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.ProductDetail__bidAmount {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--success-color);
}

.ProductDetail__bidCount {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.ProductDetail__recentBids h5 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.ProductDetail__bid {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-xs);
  border: 1px solid var(--border-light);
}

.ProductDetail__bidder {
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.ProductDetail__bidPrice {
  font-weight: 600;
  color: var(--success-color);
  font-size: var(--font-size-sm);
}

.ProductDetail__bidTime {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

/* Purchase Section */
.ProductDetail__purchase {
  background-color: var(--bg-card);
  border: 2px solid var(--primary-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
}

.ProductDetail__pricing {
  margin-bottom: var(--spacing-lg);
}

.ProductDetail__priceContainer {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.ProductDetail__price {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--primary-color);
}

.ProductDetail__originalPrice {
  font-size: var(--font-size-lg);
  color: var(--text-muted);
  text-decoration: line-through;
}

.ProductDetail__actions {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.ProductDetail__buyButton {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg) var(--spacing-xl);
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.ProductDetail__buyButton:hover {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
}

.ProductDetail__bidButton {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg) var(--spacing-md);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.ProductDetail__bidButton:hover {
  background-color: var(--accent-color);
  color: var(--text-white);
  border-color: var(--accent-color);
}

.ProductDetail__wishlistButton {
  padding: var(--spacing-lg);
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.ProductDetail__wishlistButton:hover {
  background-color: var(--danger-color);
  color: var(--text-white);
  border-color: var(--danger-color);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .ProductDetail__content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .ProductDetail__title {
    font-size: var(--font-size-2xl);
  }

  .ProductDetail__actions {
    flex-direction: column;
  }

  .ProductDetail__buyButton,
  .ProductDetail__bidButton {
    flex: none;
    width: 100%;
  }

  .ProductDetail__bid {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
}
