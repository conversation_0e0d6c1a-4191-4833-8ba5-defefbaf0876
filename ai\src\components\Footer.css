.Footer {
  background-color: var(--bg-dark);
  color: var(--text-white);
  margin-top: auto;
}

.Footer__content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-2xl);
  padding: var(--spacing-2xl) 0;
}

.Footer__section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.Footer__title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--spacing-sm);
}

.Footer__description {
  color: #94a3b8;
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
}

.Footer__social {
  display: flex;
  gap: var(--spacing-md);
}

.Footer__socialLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  border-radius: 50%;
  text-decoration: none;
  transition: all var(--transition-fast);
  font-size: var(--font-size-lg);
}

.Footer__socialLink:hover {
  background-color: var(--primary-color);
  transform: translateY(-2px);
}

.Footer__sectionTitle {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-white);
  margin-bottom: var(--spacing-sm);
}

.Footer__links {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.Footer__link {
  color: #94a3b8;
  text-decoration: none;
  transition: color var(--transition-fast);
  font-size: var(--font-size-sm);
}

.Footer__link:hover {
  color: var(--text-white);
}

.Footer__contact {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.Footer__contactItem {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: #94a3b8;
  font-size: var(--font-size-sm);
}

.Footer__contactIcon {
  color: var(--primary-color);
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.Footer__bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--spacing-lg) 0;
}

.Footer__bottomContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
}

.Footer__copyright {
  color: #94a3b8;
  font-size: var(--font-size-sm);
}

.Footer__bottomLinks {
  display: flex;
  gap: var(--spacing-lg);
}

.Footer__bottomLink {
  color: #94a3b8;
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}

.Footer__bottomLink:hover {
  color: var(--text-white);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .Footer__content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl) 0;
  }

  .Footer__bottomContent {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .Footer__bottomLinks {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .Footer__content {
    gap: var(--spacing-lg);
    padding: var(--spacing-lg) 0;
  }

  .Footer__social {
    justify-content: center;
  }

  .Footer__bottomLinks {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}
