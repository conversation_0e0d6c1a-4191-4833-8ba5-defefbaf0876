.PriceFilter {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-light);
}

.PriceFilter__title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.PriceFilter__sliders {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.PriceFilter__sliderGroup {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.PriceFilter__label {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-secondary);
}

.PriceFilter__slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  border-radius: var(--radius-sm);
  background: var(--bg-secondary);
  outline: none;
  cursor: pointer;
}

.PriceFilter__slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: 2px solid var(--bg-card);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.PriceFilter__slider::-webkit-slider-thumb:hover {
  background: var(--primary-hover);
  transform: scale(1.1);
}

.PriceFilter__slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: 2px solid var(--bg-card);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.PriceFilter__slider::-moz-range-thumb:hover {
  background: var(--primary-hover);
  transform: scale(1.1);
}

.PriceFilter__slider--min::-webkit-slider-thumb {
  background: var(--success-color);
}

.PriceFilter__slider--max::-webkit-slider-thumb {
  background: var(--danger-color);
}

.PriceFilter__slider--min::-moz-range-thumb {
  background: var(--success-color);
}

.PriceFilter__slider--max::-moz-range-thumb {
  background: var(--danger-color);
}

.PriceFilter__display {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.PriceFilter__range {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-color);
  background-color: var(--bg-secondary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  display: inline-block;
}

.PriceFilter__presets {
  margin-bottom: var(--spacing-lg);
}

.PriceFilter__presetsTitle {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.PriceFilter__presetButtons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.PriceFilter__presetButton {
  padding: var(--spacing-sm) var(--spacing-md);
  background: none;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  text-align: left;
}

.PriceFilter__presetButton:hover {
  background-color: var(--bg-secondary);
  border-color: var(--primary-color);
  color: var(--text-primary);
}

.PriceFilter__presetButton--active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--text-white);
}

.PriceFilter__presetButton--active:hover {
  background-color: var(--primary-hover);
}

.PriceFilter__reset {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.PriceFilter__reset:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .PriceFilter__presetButtons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
  }

  .PriceFilter__presetButton {
    text-align: center;
    font-size: var(--font-size-xs);
  }
}
