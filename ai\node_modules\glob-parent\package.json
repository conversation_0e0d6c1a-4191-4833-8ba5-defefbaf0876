{"name": "glob-parent", "version": "6.0.2", "description": "Extract the non-magic parent path from a glob string.", "author": "Gulp Team <<EMAIL>> (https://gulpjs.com/)", "contributors": ["<PERSON><PERSON> (https://github.com/es128)", "<PERSON> <<EMAIL>>"], "repository": "gulpjs/glob-parent", "license": "ISC", "engines": {"node": ">=10.13.0"}, "main": "index.js", "files": ["LICENSE", "index.js"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "dependencies": {"is-glob": "^4.0.3"}, "devDependencies": {"eslint": "^7.0.0", "eslint-config-gulp": "^5.0.0", "expect": "^26.0.1", "mocha": "^7.1.2", "nyc": "^15.0.1"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"]}