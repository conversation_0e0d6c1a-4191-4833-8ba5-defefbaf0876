import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FiPlus, FiEdit, FiTrash2, FiDollarSign, FiEye, 
  FiTrendingUp, FiUsers, FiStar, FiUpload 
} from 'react-icons/fi';
import { useAuth } from '../context/AuthContext';
import { useProducts } from '../context/ProductContext';
import ProductCard from '../components/ProductCard';
import AddProductModal from '../components/AddProductModal';
import './SellerDashboard.css';

const SellerDashboard = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated, isSeller } = useAuth();
  const { products, addProduct, updateProduct, deleteProduct } = useProducts();
  
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedTab, setSelectedTab] = useState('overview');

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
    
    if (!isSeller) {
      navigate('/');
      return;
    }
  }, [isAuthenticated, isSeller, navigate]);

  if (!isAuthenticated || !isSeller) {
    return null;
  }

  const sellerProducts = products.filter(product => product.seller.id === user.id);
  const totalSales = sellerProducts.reduce((sum, product) => sum + (product.seller.totalSales || 0), 0);
  const totalRevenue = sellerProducts.reduce((sum, product) => sum + (product.price * (product.seller.totalSales || 0)), 0);
  const averageRating = sellerProducts.length > 0 
    ? sellerProducts.reduce((sum, product) => sum + product.rating, 0) / sellerProducts.length 
    : 0;

  const handleAddProduct = async (productData) => {
    const result = await addProduct({
      ...productData,
      seller: {
        id: user.id,
        name: user.name,
        avatar: user.avatar,
        rating: user.rating || 0,
        totalSales: user.totalSales || 0
      }
    });

    if (result.success) {
      setShowAddModal(false);
    }

    return result;
  };

  const handleDeleteProduct = async (productId) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      await deleteProduct(productId);
    }
  };

  const stats = [
    {
      icon: FiDollarSign,
      label: 'Total Revenue',
      value: `$${totalRevenue.toFixed(2)}`,
      color: 'success'
    },
    {
      icon: FiUsers,
      label: 'Total Sales',
      value: totalSales.toString(),
      color: 'primary'
    },
    {
      icon: FiStar,
      label: 'Average Rating',
      value: averageRating.toFixed(1),
      color: 'warning'
    },
    {
      icon: FiEye,
      label: 'Products Listed',
      value: sellerProducts.length.toString(),
      color: 'info'
    }
  ];

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FiTrendingUp },
    { id: 'products', label: 'My Products', icon: FiUpload },
    { id: 'bids', label: 'Bids & Offers', icon: FiDollarSign }
  ];

  return (
    <div className="SellerDashboard">
      <div className="container">
        <div className="SellerDashboard__header">
          <div className="SellerDashboard__headerContent">
            <h1 className="SellerDashboard__title">Seller Dashboard</h1>
            <p className="SellerDashboard__subtitle">
              Manage your content, track sales, and grow your business
            </p>
          </div>
          <button 
            className="SellerDashboard__addButton"
            onClick={() => setShowAddModal(true)}
          >
            <FiPlus />
            Add New Product
          </button>
        </div>

        {/* Stats Cards */}
        <div className="SellerDashboard__stats">
          {stats.map((stat, index) => (
            <div key={index} className={`SellerDashboard__stat SellerDashboard__stat--${stat.color}`}>
              <div className="SellerDashboard__statIcon">
                <stat.icon />
              </div>
              <div className="SellerDashboard__statContent">
                <span className="SellerDashboard__statValue">{stat.value}</span>
                <span className="SellerDashboard__statLabel">{stat.label}</span>
              </div>
            </div>
          ))}
        </div>

        {/* Tabs */}
        <div className="SellerDashboard__tabs">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`SellerDashboard__tab ${
                selectedTab === tab.id ? 'SellerDashboard__tab--active' : ''
              }`}
              onClick={() => setSelectedTab(tab.id)}
            >
              <tab.icon className="SellerDashboard__tabIcon" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="SellerDashboard__content">
          {selectedTab === 'overview' && (
            <div className="SellerDashboard__overview">
              <div className="SellerDashboard__section">
                <h2 className="SellerDashboard__sectionTitle">Recent Activity</h2>
                <div className="SellerDashboard__activity">
                  <p className="SellerDashboard__noActivity">
                    No recent activity to display. Start by adding your first product!
                  </p>
                </div>
              </div>

              <div className="SellerDashboard__section">
                <h2 className="SellerDashboard__sectionTitle">Quick Actions</h2>
                <div className="SellerDashboard__quickActions">
                  <button 
                    className="SellerDashboard__quickAction"
                    onClick={() => setShowAddModal(true)}
                  >
                    <FiPlus className="SellerDashboard__quickActionIcon" />
                    <span>Add Product</span>
                  </button>
                  <button 
                    className="SellerDashboard__quickAction"
                    onClick={() => setSelectedTab('products')}
                  >
                    <FiEdit className="SellerDashboard__quickActionIcon" />
                    <span>Manage Products</span>
                  </button>
                  <button 
                    className="SellerDashboard__quickAction"
                    onClick={() => setSelectedTab('bids')}
                  >
                    <FiDollarSign className="SellerDashboard__quickActionIcon" />
                    <span>View Bids</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {selectedTab === 'products' && (
            <div className="SellerDashboard__products">
              <div className="SellerDashboard__sectionHeader">
                <h2 className="SellerDashboard__sectionTitle">My Products</h2>
                <button 
                  className="SellerDashboard__addButton SellerDashboard__addButton--small"
                  onClick={() => setShowAddModal(true)}
                >
                  <FiPlus />
                  Add Product
                </button>
              </div>

              {sellerProducts.length > 0 ? (
                <div className="SellerDashboard__productsGrid">
                  {sellerProducts.map(product => (
                    <div key={product.id} className="SellerDashboard__productCard">
                      <ProductCard product={product} />
                      <div className="SellerDashboard__productActions">
                        <button 
                          className="SellerDashboard__productAction SellerDashboard__productAction--edit"
                          onClick={() => navigate(`/product/${product.id}`)}
                        >
                          <FiEye />
                          View
                        </button>
                        <button 
                          className="SellerDashboard__productAction SellerDashboard__productAction--edit"
                          title="Edit product"
                        >
                          <FiEdit />
                          Edit
                        </button>
                        <button 
                          className="SellerDashboard__productAction SellerDashboard__productAction--delete"
                          onClick={() => handleDeleteProduct(product.id)}
                          title="Delete product"
                        >
                          <FiTrash2 />
                          Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="SellerDashboard__emptyState">
                  <FiUpload className="SellerDashboard__emptyIcon" />
                  <h3>No products yet</h3>
                  <p>Start selling by adding your first product</p>
                  <button 
                    className="SellerDashboard__addButton"
                    onClick={() => setShowAddModal(true)}
                  >
                    <FiPlus />
                    Add Your First Product
                  </button>
                </div>
              )}
            </div>
          )}

          {selectedTab === 'bids' && (
            <div className="SellerDashboard__bids">
              <h2 className="SellerDashboard__sectionTitle">Bids & Offers</h2>
              
              {sellerProducts.some(product => product.bids.length > 0) ? (
                <div className="SellerDashboard__bidsList">
                  {sellerProducts.map(product => 
                    product.bids.length > 0 && (
                      <div key={product.id} className="SellerDashboard__productBids">
                        <h3 className="SellerDashboard__productTitle">{product.title}</h3>
                        <div className="SellerDashboard__bidsContainer">
                          {product.bids.map(bid => (
                            <div key={bid.id} className="SellerDashboard__bid">
                              <div className="SellerDashboard__bidInfo">
                                <span className="SellerDashboard__bidder">{bid.buyerName}</span>
                                <span className="SellerDashboard__bidAmount">${bid.amount.toFixed(2)}</span>
                                <span className="SellerDashboard__bidTime">
                                  {new Date(bid.timestamp).toLocaleDateString()}
                                </span>
                              </div>
                              <div className="SellerDashboard__bidActions">
                                <button className="SellerDashboard__bidAction SellerDashboard__bidAction--accept">
                                  Accept
                                </button>
                                <button className="SellerDashboard__bidAction SellerDashboard__bidAction--decline">
                                  Decline
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )
                  )}
                </div>
              ) : (
                <div className="SellerDashboard__emptyState">
                  <FiDollarSign className="SellerDashboard__emptyIcon" />
                  <h3>No bids yet</h3>
                  <p>Bids and offers on your products will appear here</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Add Product Modal */}
      {showAddModal && (
        <AddProductModal
          onClose={() => setShowAddModal(false)}
          onSubmit={handleAddProduct}
        />
      )}
    </div>
  );
};

export default SellerDashboard;
