.ProductCard {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  overflow: hidden;
  transition: all var(--transition-normal);
  height: 100%;
}

.ProductCard:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.ProductCard__link {
  display: block;
  text-decoration: none;
  color: inherit;
  height: 100%;
}

.ProductCard__thumbnail {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.ProductCard__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.ProductCard:hover .ProductCard__image {
  transform: scale(1.05);
}

.ProductCard__overlay {
  position: absolute;
  top: var(--spacing-sm);
  left: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background-color: rgba(0, 0, 0, 0.7);
  color: var(--text-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.ProductCard__typeIcon {
  font-size: var(--font-size-sm);
}

.ProductCard__discount {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background-color: var(--danger-color);
  color: var(--text-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.ProductCard__content {
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  height: calc(100% - 200px);
}

.ProductCard__header {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.ProductCard__title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.ProductCard__meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.ProductCard__level {
  background-color: var(--bg-secondary);
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.ProductCard__separator {
  color: var(--text-muted);
}

.ProductCard__duration {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.ProductCard__metaIcon {
  font-size: var(--font-size-xs);
}

.ProductCard__description {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  margin: 0;
  flex-grow: 1;
}

.ProductCard__tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.ProductCard__tag {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.ProductCard__tagMore {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.ProductCard__rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.ProductCard__stars {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.ProductCard__starIcon {
  color: var(--accent-color);
  font-size: var(--font-size-sm);
}

.ProductCard__ratingValue {
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.ProductCard__reviews {
  color: var(--text-muted);
  font-size: var(--font-size-xs);
}

.ProductCard__seller {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.ProductCard__sellerAvatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.ProductCard__sellerInfo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  flex-grow: 1;
}

.ProductCard__sellerName {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
}

.ProductCard__sellerRating {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.ProductCard__sellerStarIcon {
  color: var(--accent-color);
  font-size: var(--font-size-xs);
}

.ProductCard__bidding {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm);
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.ProductCard__bidInfo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.ProductCard__bidLabel {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.ProductCard__bidAmount {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--success-color);
}

.ProductCard__bidCount {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.ProductCard__pricing {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
  margin-top: auto;
}

.ProductCard__priceContainer {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.ProductCard__price {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
}

.ProductCard__originalPrice {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  text-decoration: line-through;
}

.ProductCard__buyButton {
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.ProductCard__buyButton:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .ProductCard__content {
    padding: var(--spacing-md);
  }

  .ProductCard__title {
    font-size: var(--font-size-base);
  }

  .ProductCard__pricing {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .ProductCard__buyButton {
    width: 100%;
    text-align: center;
  }
}
