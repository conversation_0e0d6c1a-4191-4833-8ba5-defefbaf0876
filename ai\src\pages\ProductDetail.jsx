import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  FiStar, FiClock, FiBookOpen, FiPlay, FiFileText, 
  FiUser, FiDollarSign, FiShoppingCart, FiHeart 
} from 'react-icons/fi';
import { useProducts } from '../context/ProductContext';
import { useAuth } from '../context/AuthContext';
import BiddingModal from '../components/BiddingModal';
import './ProductDetail.css';

const ProductDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { getProductById, addBid } = useProducts();
  const { user, isAuthenticated } = useAuth();
  
  const [product, setProduct] = useState(null);
  const [showBiddingModal, setShowBiddingModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchProduct = () => {
      const foundProduct = getProductById(id);
      if (foundProduct) {
        setProduct(foundProduct);
      } else {
        navigate('/');
      }
      setIsLoading(false);
    };

    fetchProduct();
  }, [id, getProductById, navigate]);

  const handleBidSubmit = async (bidAmount) => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    const result = await addBid(product.id, {
      amount: bidAmount,
      buyerId: user.id,
      buyerName: user.name
    });

    if (result.success) {
      // Refresh product data
      const updatedProduct = getProductById(id);
      setProduct(updatedProduct);
      setShowBiddingModal(false);
    }

    return result;
  };

  const handlePurchase = () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
    // In a real app, this would integrate with a payment system
    alert('Purchase functionality would be integrated with a payment system');
  };

  const getTypeIcon = () => {
    switch (product?.type) {
      case 'video':
        return <FiPlay className="ProductDetail__typeIcon" />;
      case 'ebook':
        return <FiBookOpen className="ProductDetail__typeIcon" />;
      case 'plan':
        return <FiFileText className="ProductDetail__typeIcon" />;
      default:
        return <FiFileText className="ProductDetail__typeIcon" />;
    }
  };

  const getTypeInfo = () => {
    if (!product) return '';
    if (product.type === 'video' && product.duration) return product.duration;
    if (product.type === 'ebook' && product.pages) return `${product.pages} pages`;
    if (product.type === 'plan' && product.duration) return product.duration;
    return '';
  };

  if (isLoading) {
    return (
      <div className="ProductDetail__loading">
        <div className="container">
          <div className="ProductDetail__loadingContent">
            <h2>Loading product details...</h2>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="ProductDetail__notFound">
        <div className="container">
          <div className="ProductDetail__notFoundContent">
            <h2>Product not found</h2>
            <p>The product you're looking for doesn't exist.</p>
            <button onClick={() => navigate('/')} className="btn-primary">
              Back to Homepage
            </button>
          </div>
        </div>
      </div>
    );
  }

  const hasDiscount = product.originalPrice && product.originalPrice > product.price;
  const discountPercentage = hasDiscount 
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0;

  const highestBid = product.bids.length > 0 
    ? Math.max(...product.bids.map(bid => bid.amount))
    : null;

  return (
    <div className="ProductDetail">
      <div className="container">
        <div className="ProductDetail__content">
          {/* Media Section */}
          <div className="ProductDetail__media">
            <div className="ProductDetail__imageContainer">
              <img 
                src={product.preview} 
                alt={product.title}
                className="ProductDetail__image"
              />
              <div className="ProductDetail__overlay">
                {getTypeIcon()}
                <span className="ProductDetail__typeText">
                  {product.type.charAt(0).toUpperCase() + product.type.slice(1)}
                </span>
              </div>
              {hasDiscount && (
                <div className="ProductDetail__discount">
                  -{discountPercentage}%
                </div>
              )}
            </div>

            {/* Additional Images/Preview */}
            <div className="ProductDetail__thumbnails">
              <img 
                src={product.thumbnail} 
                alt={`${product.title} thumbnail`}
                className="ProductDetail__thumbnail"
              />
              {/* In a real app, there would be multiple images */}
            </div>
          </div>

          {/* Product Info */}
          <div className="ProductDetail__info">
            <div className="ProductDetail__header">
              <div className="ProductDetail__meta">
                <span className="ProductDetail__level">{product.level}</span>
                {getTypeInfo() && (
                  <>
                    <span className="ProductDetail__separator">•</span>
                    <span className="ProductDetail__duration">
                      <FiClock className="ProductDetail__metaIcon" />
                      {getTypeInfo()}
                    </span>
                  </>
                )}
              </div>
              
              <h1 className="ProductDetail__title">{product.title}</h1>
              
              <div className="ProductDetail__rating">
                <div className="ProductDetail__stars">
                  <FiStar className="ProductDetail__starIcon" />
                  <span className="ProductDetail__ratingValue">{product.rating}</span>
                </div>
                <span className="ProductDetail__reviews">
                  ({product.reviews} reviews)
                </span>
              </div>
            </div>

            <div className="ProductDetail__description">
              <h3>Description</h3>
              <p>{product.description}</p>
            </div>

            <div className="ProductDetail__tags">
              <h4>Tags</h4>
              <div className="ProductDetail__tagList">
                {product.tags.map((tag, index) => (
                  <span key={index} className="ProductDetail__tag">
                    {tag}
                  </span>
                ))}
              </div>
            </div>

            {/* Seller Info */}
            <div className="ProductDetail__seller">
              <h4>About the Instructor</h4>
              <div className="ProductDetail__sellerCard">
                <img 
                  src={product.seller.avatar} 
                  alt={product.seller.name}
                  className="ProductDetail__sellerAvatar"
                />
                <div className="ProductDetail__sellerInfo">
                  <h5 className="ProductDetail__sellerName">{product.seller.name}</h5>
                  <div className="ProductDetail__sellerRating">
                    <FiStar className="ProductDetail__sellerStarIcon" />
                    <span>{product.seller.rating}</span>
                    <span className="ProductDetail__sellerSales">
                      • {product.seller.totalSales} sales
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Bidding Section */}
            {product.bids.length > 0 && (
              <div className="ProductDetail__bidding">
                <h4>Current Bids</h4>
                <div className="ProductDetail__bidInfo">
                  <div className="ProductDetail__highestBid">
                    <span className="ProductDetail__bidLabel">Highest Bid:</span>
                    <span className="ProductDetail__bidAmount">${highestBid.toFixed(2)}</span>
                  </div>
                  <span className="ProductDetail__bidCount">
                    {product.bids.length} bid{product.bids.length !== 1 ? 's' : ''}
                  </span>
                </div>
                
                <div className="ProductDetail__recentBids">
                  <h5>Recent Bids</h5>
                  {product.bids.slice(-3).reverse().map((bid) => (
                    <div key={bid.id} className="ProductDetail__bid">
                      <span className="ProductDetail__bidder">{bid.buyerName}</span>
                      <span className="ProductDetail__bidPrice">${bid.amount.toFixed(2)}</span>
                      <span className="ProductDetail__bidTime">
                        {new Date(bid.timestamp).toLocaleDateString()}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Purchase Section */}
            <div className="ProductDetail__purchase">
              <div className="ProductDetail__pricing">
                <div className="ProductDetail__priceContainer">
                  <span className="ProductDetail__price">${product.price.toFixed(2)}</span>
                  {hasDiscount && (
                    <span className="ProductDetail__originalPrice">
                      ${product.originalPrice.toFixed(2)}
                    </span>
                  )}
                </div>
              </div>

              <div className="ProductDetail__actions">
                <button 
                  className="ProductDetail__buyButton"
                  onClick={handlePurchase}
                >
                  <FiShoppingCart />
                  Buy Now
                </button>
                
                <button 
                  className="ProductDetail__bidButton"
                  onClick={() => setShowBiddingModal(true)}
                >
                  <FiDollarSign />
                  Make Offer
                </button>
                
                <button className="ProductDetail__wishlistButton">
                  <FiHeart />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bidding Modal */}
      {showBiddingModal && (
        <BiddingModal
          product={product}
          onClose={() => setShowBiddingModal(false)}
          onSubmit={handleBidSubmit}
        />
      )}
    </div>
  );
};

export default ProductDetail;
