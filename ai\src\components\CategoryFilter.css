.CategoryFilter {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-light);
}

.CategoryFilter__title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.CategoryFilter__options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.CategoryFilter__option {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: none;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: left;
  width: 100%;
}

.CategoryFilter__option:hover {
  background-color: var(--bg-secondary);
}

.CategoryFilter__option--active {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.CategoryFilter__option--active:hover {
  background-color: var(--primary-hover);
}

.CategoryFilter__icon {
  font-size: var(--font-size-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
}

.CategoryFilter__name {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .CategoryFilter__options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-sm);
  }

  .CategoryFilter__option {
    flex-direction: column;
    text-align: center;
    padding: var(--spacing-md);
  }

  .CategoryFilter__icon {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-xs);
  }

  .CategoryFilter__name {
    font-size: var(--font-size-xs);
  }
}
