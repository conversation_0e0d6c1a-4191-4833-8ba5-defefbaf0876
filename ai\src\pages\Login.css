.Login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: var(--spacing-xl) 0;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.Login__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  align-items: center;
  max-width: 1000px;
  margin: 0 auto;
}

.Login__form {
  background-color: var(--bg-card);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-light);
}

.Login__header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.Login__title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.Login__subtitle {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  line-height: 1.6;
}

.Login__formContainer {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.Login__field {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.Login__label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.Login__icon {
  font-size: var(--font-size-lg);
  color: var(--primary-color);
}

.Login__input,
.Login__select {
  padding: var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  transition: all var(--transition-fast);
  background-color: var(--bg-card);
}

.Login__input:focus,
.Login__select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.Login__input--error {
  border-color: var(--danger-color);
}

.Login__passwordField {
  position: relative;
}

.Login__passwordToggle {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  font-size: var(--font-size-lg);
  padding: var(--spacing-xs);
}

.Login__passwordToggle:hover {
  color: var(--text-primary);
}

.Login__error {
  font-size: var(--font-size-sm);
  color: var(--danger-color);
  margin-top: var(--spacing-xs);
}

.Login__submitError {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  text-align: center;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.Login__submitButton {
  padding: var(--spacing-md) var(--spacing-xl);
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-top: var(--spacing-md);
}

.Login__submitButton:hover:not(:disabled) {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.Login__submitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.Login__footer {
  text-align: center;
  margin-top: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.Login__switchText {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.Login__switchButton {
  background: none;
  border: none;
  color: var(--primary-color);
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  font-size: var(--font-size-sm);
}

.Login__switchButton:hover {
  color: var(--primary-hover);
}

.Login__forgotLink {
  color: var(--primary-color);
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.Login__forgotLink:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

.Login__demo {
  margin-top: var(--spacing-xl);
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.Login__demo h4 {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.Login__demo p {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin: var(--spacing-xs) 0;
}

.Login__image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.Login__img {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .Login__content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .Login__image {
    order: -1;
  }

  .Login__form {
    padding: var(--spacing-xl);
  }

  .Login__title {
    font-size: var(--font-size-2xl);
  }

  .Login__footer {
    margin-top: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .Login {
    padding: var(--spacing-md) 0;
  }

  .Login__form {
    padding: var(--spacing-lg);
  }

  .Login__formContainer {
    gap: var(--spacing-md);
  }
}
