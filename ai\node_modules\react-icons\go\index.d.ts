// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const GoAlert: IconType;
export declare const GoAlertFill: IconType;
export declare const GoArchive: IconType;
export declare const GoArrowBoth: IconType;
export declare const GoArrowDown: IconType;
export declare const GoArrowDownLeft: IconType;
export declare const GoArrowDownRight: IconType;
export declare const GoArrowLeft: IconType;
export declare const GoArrowRight: IconType;
export declare const GoArrowSwitch: IconType;
export declare const GoArrowUp: IconType;
export declare const GoArrowUpLeft: IconType;
export declare const GoArrowUpRight: IconType;
export declare const GoBeaker: IconType;
export declare const GoBell: IconType;
export declare const GoBellFill: IconType;
export declare const GoBellSlash: IconType;
export declare const GoBlocked: IconType;
export declare const GoBold: IconType;
export declare const GoBook: IconType;
export declare const GoBookmark: IconType;
export declare const GoBookmarkFill: IconType;
export declare const GoBookmarkSlash: IconType;
export declare const GoBookmarkSlashFill: IconType;
export declare const GoBriefcase: IconType;
export declare const GoBroadcast: IconType;
export declare const GoBrowser: IconType;
export declare const GoBug: IconType;
export declare const GoCalendar: IconType;
export declare const GoCheck: IconType;
export declare const GoCheckCircle: IconType;
export declare const GoCheckCircleFill: IconType;
export declare const GoCheckbox: IconType;
export declare const GoChecklist: IconType;
export declare const GoChevronDown: IconType;
export declare const GoChevronLeft: IconType;
export declare const GoChevronRight: IconType;
export declare const GoChevronUp: IconType;
export declare const GoCircle: IconType;
export declare const GoCircleSlash: IconType;
export declare const GoClock: IconType;
export declare const GoClockFill: IconType;
export declare const GoCloud: IconType;
export declare const GoCloudOffline: IconType;
export declare const GoCode: IconType;
export declare const GoCodeOfConduct: IconType;
export declare const GoCodeReview: IconType;
export declare const GoCodeSquare: IconType;
export declare const GoCodescan: IconType;
export declare const GoCodescanCheckmark: IconType;
export declare const GoCodespaces: IconType;
export declare const GoColumns: IconType;
export declare const GoCommandPalette: IconType;
export declare const GoComment: IconType;
export declare const GoCommentDiscussion: IconType;
export declare const GoCommit: IconType;
export declare const GoContainer: IconType;
export declare const GoCopilot: IconType;
export declare const GoCopy: IconType;
export declare const GoCpu: IconType;
export declare const GoCreditCard: IconType;
export declare const GoCrossReference: IconType;
export declare const GoDash: IconType;
export declare const GoDatabase: IconType;
export declare const GoDependabot: IconType;
export declare const GoDesktopDownload: IconType;
export declare const GoDeviceCameraVideo: IconType;
export declare const GoDeviceDesktop: IconType;
export declare const GoDeviceMobile: IconType;
export declare const GoDiamond: IconType;
export declare const GoDiff: IconType;
export declare const GoDiscussionClosed: IconType;
export declare const GoDiscussionDuplicate: IconType;
export declare const GoDiscussionOutdated: IconType;
export declare const GoDot: IconType;
export declare const GoDotFill: IconType;
export declare const GoDownload: IconType;
export declare const GoDuplicate: IconType;
export declare const GoEye: IconType;
export declare const GoEyeClosed: IconType;
export declare const GoFile: IconType;
export declare const GoFileBinary: IconType;
export declare const GoFileCode: IconType;
export declare const GoFileDiff: IconType;
export declare const GoFileDirectory: IconType;
export declare const GoFileDirectoryFill: IconType;
export declare const GoFileMedia: IconType;
export declare const GoFileSubmodule: IconType;
export declare const GoFileSymlinkFile: IconType;
export declare const GoFileZip: IconType;
export declare const GoFilter: IconType;
export declare const GoFlame: IconType;
export declare const GoFold: IconType;
export declare const GoFoldDown: IconType;
export declare const GoFoldUp: IconType;
export declare const GoGear: IconType;
export declare const GoGift: IconType;
export declare const GoGitBranch: IconType;
export declare const GoGitCommit: IconType;
export declare const GoGitCompare: IconType;
export declare const GoGitMerge: IconType;
export declare const GoGitMergeQueue: IconType;
export declare const GoGitPullRequest: IconType;
export declare const GoGitPullRequestClosed: IconType;
export declare const GoGitPullRequestDraft: IconType;
export declare const GoGlobe: IconType;
export declare const GoGoal: IconType;
export declare const GoGrabber: IconType;
export declare const GoGraph: IconType;
export declare const GoHash: IconType;
export declare const GoHeading: IconType;
export declare const GoHeart: IconType;
export declare const GoHeartFill: IconType;
export declare const GoHistory: IconType;
export declare const GoHome: IconType;
export declare const GoHomeFill: IconType;
export declare const GoHorizontalRule: IconType;
export declare const GoHourglass: IconType;
export declare const GoHubot: IconType;
export declare const GoImage: IconType;
export declare const GoInbox: IconType;
export declare const GoInfinity: IconType;
export declare const GoInfo: IconType;
export declare const GoIssueClosed: IconType;
export declare const GoIssueDraft: IconType;
export declare const GoIssueOpened: IconType;
export declare const GoIssueReopened: IconType;
export declare const GoIssueTrackedBy: IconType;
export declare const GoIssueTracks: IconType;
export declare const GoItalic: IconType;
export declare const GoIterations: IconType;
export declare const GoKebabHorizontal: IconType;
export declare const GoKey: IconType;
export declare const GoLaw: IconType;
export declare const GoLightBulb: IconType;
export declare const GoLink: IconType;
export declare const GoLinkExternal: IconType;
export declare const GoListOrdered: IconType;
export declare const GoListUnordered: IconType;
export declare const GoLocation: IconType;
export declare const GoLock: IconType;
export declare const GoLog: IconType;
export declare const GoMail: IconType;
export declare const GoMegaphone: IconType;
export declare const GoMention: IconType;
export declare const GoMilestone: IconType;
export declare const GoMirror: IconType;
export declare const GoMoon: IconType;
export declare const GoMortarBoard: IconType;
export declare const GoMoveToBottom: IconType;
export declare const GoMoveToEnd: IconType;
export declare const GoMoveToStart: IconType;
export declare const GoMoveToTop: IconType;
export declare const GoMultiSelect: IconType;
export declare const GoMute: IconType;
export declare const GoNoEntry: IconType;
export declare const GoNorthStar: IconType;
export declare const GoNote: IconType;
export declare const GoNumber: IconType;
export declare const GoOrganization: IconType;
export declare const GoPackage: IconType;
export declare const GoPackageDependencies: IconType;
export declare const GoPackageDependents: IconType;
export declare const GoPaperAirplane: IconType;
export declare const GoPaperclip: IconType;
export declare const GoPasskeyFill: IconType;
export declare const GoPaste: IconType;
export declare const GoPencil: IconType;
export declare const GoPeople: IconType;
export declare const GoPerson: IconType;
export declare const GoPersonAdd: IconType;
export declare const GoPersonFill: IconType;
export declare const GoPin: IconType;
export declare const GoPlay: IconType;
export declare const GoPlug: IconType;
export declare const GoPlus: IconType;
export declare const GoPlusCircle: IconType;
export declare const GoProject: IconType;
export declare const GoProjectRoadmap: IconType;
export declare const GoProjectSymlink: IconType;
export declare const GoProjectTemplate: IconType;
export declare const GoPulse: IconType;
export declare const GoQuestion: IconType;
export declare const GoQuote: IconType;
export declare const GoRead: IconType;
export declare const GoRelFilePath: IconType;
export declare const GoReply: IconType;
export declare const GoRepo: IconType;
export declare const GoRepoForked: IconType;
export declare const GoRepoLocked: IconType;
export declare const GoRepoPush: IconType;
export declare const GoRepoTemplate: IconType;
export declare const GoReport: IconType;
export declare const GoRocket: IconType;
export declare const GoRows: IconType;
export declare const GoRss: IconType;
export declare const GoRuby: IconType;
export declare const GoScreenFull: IconType;
export declare const GoScreenNormal: IconType;
export declare const GoSearch: IconType;
export declare const GoServer: IconType;
export declare const GoShare: IconType;
export declare const GoShareAndroid: IconType;
export declare const GoShield: IconType;
export declare const GoShieldCheck: IconType;
export declare const GoShieldLock: IconType;
export declare const GoShieldSlash: IconType;
export declare const GoShieldX: IconType;
export declare const GoSidebarCollapse: IconType;
export declare const GoSidebarExpand: IconType;
export declare const GoSignIn: IconType;
export declare const GoSignOut: IconType;
export declare const GoSingleSelect: IconType;
export declare const GoSkip: IconType;
export declare const GoSkipFill: IconType;
export declare const GoSmiley: IconType;
export declare const GoSortAsc: IconType;
export declare const GoSortDesc: IconType;
export declare const GoSponsorTiers: IconType;
export declare const GoSquare: IconType;
export declare const GoSquareFill: IconType;
export declare const GoSquirrel: IconType;
export declare const GoStack: IconType;
export declare const GoStar: IconType;
export declare const GoStarFill: IconType;
export declare const GoStop: IconType;
export declare const GoStopwatch: IconType;
export declare const GoStrikethrough: IconType;
export declare const GoSun: IconType;
export declare const GoSync: IconType;
export declare const GoTab: IconType;
export declare const GoTable: IconType;
export declare const GoTag: IconType;
export declare const GoTasklist: IconType;
export declare const GoTelescope: IconType;
export declare const GoTelescopeFill: IconType;
export declare const GoTerminal: IconType;
export declare const GoThumbsdown: IconType;
export declare const GoThumbsup: IconType;
export declare const GoTools: IconType;
export declare const GoTrash: IconType;
export declare const GoTriangleDown: IconType;
export declare const GoTriangleLeft: IconType;
export declare const GoTriangleRight: IconType;
export declare const GoTriangleUp: IconType;
export declare const GoTrophy: IconType;
export declare const GoTypography: IconType;
export declare const GoUnfold: IconType;
export declare const GoUnlink: IconType;
export declare const GoUnlock: IconType;
export declare const GoUnmute: IconType;
export declare const GoUnread: IconType;
export declare const GoUnverified: IconType;
export declare const GoUpload: IconType;
export declare const GoVerified: IconType;
export declare const GoVersions: IconType;
export declare const GoVideo: IconType;
export declare const GoWorkflow: IconType;
export declare const GoX: IconType;
export declare const GoXCircle: IconType;
export declare const GoXCircleFill: IconType;
export declare const GoZap: IconType;
export declare const GoZoomIn: IconType;
export declare const GoZoomOut: IconType;
