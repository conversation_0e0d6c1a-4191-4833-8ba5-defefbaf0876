import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, Link } from 'react-router-dom';
import { FiMail, FiLock, FiUser, <PERSON>Eye, FiEyeOff } from 'react-icons/fi';
import { useAuth } from '../context/AuthContext';
import './Login.css';

const Login = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { login, register, isLoading, isAuthenticated } = useAuth();
  
  const [mode, setMode] = useState(searchParams.get('mode') === 'register' ? 'register' : 'login');
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'buyer'
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (mode === 'register' && !formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (mode === 'register') {
      if (!formData.confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      let result;
      if (mode === 'login') {
        result = await login(formData.email, formData.password);
      } else {
        result = await register({
          name: formData.name,
          email: formData.email,
          password: formData.password,
          role: formData.role
        });
      }

      if (result.success) {
        navigate('/');
      } else {
        setErrors({ submit: result.error });
      }
    } catch (error) {
      setErrors({ submit: 'An unexpected error occurred' });
    }
  };

  const toggleMode = () => {
    setMode(mode === 'login' ? 'register' : 'login');
    setFormData({
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      role: 'buyer'
    });
    setErrors({});
  };

  return (
    <div className="Login">
      <div className="container">
        <div className="Login__content">
          <div className="Login__form">
            <div className="Login__header">
              <h1 className="Login__title">
                {mode === 'login' ? 'Welcome Back' : 'Join SportsHub'}
              </h1>
              <p className="Login__subtitle">
                {mode === 'login' 
                  ? 'Sign in to access your account and continue your training journey'
                  : 'Create your account and start exploring premium sports content'
                }
              </p>
            </div>

            <form className="Login__formContainer" onSubmit={handleSubmit}>
              {mode === 'register' && (
                <div className="Login__field">
                  <label className="Login__label">
                    <FiUser className="Login__icon" />
                    Full Name
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className={`Login__input ${errors.name ? 'Login__input--error' : ''}`}
                    placeholder="Enter your full name"
                  />
                  {errors.name && <span className="Login__error">{errors.name}</span>}
                </div>
              )}

              <div className="Login__field">
                <label className="Login__label">
                  <FiMail className="Login__icon" />
                  Email Address
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`Login__input ${errors.email ? 'Login__input--error' : ''}`}
                  placeholder="Enter your email"
                />
                {errors.email && <span className="Login__error">{errors.email}</span>}
              </div>

              <div className="Login__field">
                <label className="Login__label">
                  <FiLock className="Login__icon" />
                  Password
                </label>
                <div className="Login__passwordField">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className={`Login__input ${errors.password ? 'Login__input--error' : ''}`}
                    placeholder="Enter your password"
                  />
                  <button
                    type="button"
                    className="Login__passwordToggle"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <FiEyeOff /> : <FiEye />}
                  </button>
                </div>
                {errors.password && <span className="Login__error">{errors.password}</span>}
              </div>

              {mode === 'register' && (
                <>
                  <div className="Login__field">
                    <label className="Login__label">
                      <FiLock className="Login__icon" />
                      Confirm Password
                    </label>
                    <input
                      type="password"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      className={`Login__input ${errors.confirmPassword ? 'Login__input--error' : ''}`}
                      placeholder="Confirm your password"
                    />
                    {errors.confirmPassword && <span className="Login__error">{errors.confirmPassword}</span>}
                  </div>

                  <div className="Login__field">
                    <label className="Login__label">Account Type</label>
                    <select
                      name="role"
                      value={formData.role}
                      onChange={handleInputChange}
                      className="Login__select"
                    >
                      <option value="buyer">Buyer - Purchase content</option>
                      <option value="seller">Seller - Sell your content</option>
                    </select>
                  </div>
                </>
              )}

              {errors.submit && (
                <div className="Login__submitError">
                  {errors.submit}
                </div>
              )}

              <button
                type="submit"
                className="Login__submitButton"
                disabled={isLoading}
              >
                {isLoading 
                  ? (mode === 'login' ? 'Signing In...' : 'Creating Account...') 
                  : (mode === 'login' ? 'Sign In' : 'Create Account')
                }
              </button>
            </form>

            <div className="Login__footer">
              <p className="Login__switchText">
                {mode === 'login' 
                  ? "Don't have an account? " 
                  : "Already have an account? "
                }
                <button 
                  className="Login__switchButton"
                  onClick={toggleMode}
                >
                  {mode === 'login' ? 'Sign Up' : 'Sign In'}
                </button>
              </p>

              {mode === 'login' && (
                <Link to="/forgot-password" className="Login__forgotLink">
                  Forgot your password?
                </Link>
              )}
            </div>

            {/* Demo Credentials */}
            <div className="Login__demo">
              <h4>Demo Credentials:</h4>
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Password:</strong> password123</p>
            </div>
          </div>

          <div className="Login__image">
            <img 
              src="https://images.unsplash.com/photo-*************-1cb2f99b2d8b?w=600&h=800&fit=crop" 
              alt="Sports training"
              className="Login__img"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
