import React from 'react';
import { Link } from 'react-router-dom';
import { FiStar, FiClock, FiUser, FiBookOpen, FiPlay, FiFileText } from 'react-icons/fi';
import './ProductCard.css';

const ProductCard = ({ product, showBids = false }) => {
  const {
    id,
    title,
    description,
    price,
    originalPrice,
    category,
    type,
    duration,
    pages,
    level,
    rating,
    reviews,
    seller,
    thumbnail,
    tags,
    bids = []
  } = product;

  const getTypeIcon = () => {
    switch (type) {
      case 'video':
        return <FiPlay className="ProductCard__typeIcon" />;
      case 'ebook':
        return <FiBookOpen className="ProductCard__typeIcon" />;
      case 'plan':
        return <FiFileText className="ProductCard__typeIcon" />;
      default:
        return <FiFileText className="ProductCard__typeIcon" />;
    }
  };

  const getTypeInfo = () => {
    if (type === 'video' && duration) return duration;
    if (type === 'ebook' && pages) return `${pages} pages`;
    if (type === 'plan' && duration) return duration;
    return '';
  };

  const hasDiscount = originalPrice && originalPrice > price;
  const discountPercentage = hasDiscount 
    ? Math.round(((originalPrice - price) / originalPrice) * 100)
    : 0;

  const highestBid = bids.length > 0 
    ? Math.max(...bids.map(bid => bid.amount))
    : null;

  return (
    <div className="ProductCard">
      <Link to={`/product/${id}`} className="ProductCard__link">
        {/* Thumbnail */}
        <div className="ProductCard__thumbnail">
          <img 
            src={thumbnail} 
            alt={title}
            className="ProductCard__image"
          />
          <div className="ProductCard__overlay">
            {getTypeIcon()}
            <span className="ProductCard__typeText">
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </span>
          </div>
          {hasDiscount && (
            <div className="ProductCard__discount">
              -{discountPercentage}%
            </div>
          )}
        </div>

        {/* Content */}
        <div className="ProductCard__content">
          {/* Header */}
          <div className="ProductCard__header">
            <h3 className="ProductCard__title">{title}</h3>
            <div className="ProductCard__meta">
              <span className="ProductCard__level">{level}</span>
              {getTypeInfo() && (
                <>
                  <span className="ProductCard__separator">•</span>
                  <span className="ProductCard__duration">
                    <FiClock className="ProductCard__metaIcon" />
                    {getTypeInfo()}
                  </span>
                </>
              )}
            </div>
          </div>

          {/* Description */}
          <p className="ProductCard__description">
            {description.length > 120 
              ? `${description.substring(0, 120)}...` 
              : description
            }
          </p>

          {/* Tags */}
          <div className="ProductCard__tags">
            {tags.slice(0, 3).map((tag, index) => (
              <span key={index} className="ProductCard__tag">
                {tag}
              </span>
            ))}
            {tags.length > 3 && (
              <span className="ProductCard__tag ProductCard__tagMore">
                +{tags.length - 3}
              </span>
            )}
          </div>

          {/* Rating & Reviews */}
          <div className="ProductCard__rating">
            <div className="ProductCard__stars">
              <FiStar className="ProductCard__starIcon" />
              <span className="ProductCard__ratingValue">{rating}</span>
            </div>
            <span className="ProductCard__reviews">
              ({reviews} reviews)
            </span>
          </div>

          {/* Seller */}
          <div className="ProductCard__seller">
            <img 
              src={seller.avatar} 
              alt={seller.name}
              className="ProductCard__sellerAvatar"
            />
            <div className="ProductCard__sellerInfo">
              <span className="ProductCard__sellerName">{seller.name}</span>
              <div className="ProductCard__sellerRating">
                <FiStar className="ProductCard__sellerStarIcon" />
                <span>{seller.rating}</span>
              </div>
            </div>
          </div>

          {/* Bidding Info */}
          {showBids && bids.length > 0 && (
            <div className="ProductCard__bidding">
              <div className="ProductCard__bidInfo">
                <span className="ProductCard__bidLabel">Highest Bid:</span>
                <span className="ProductCard__bidAmount">${highestBid.toFixed(2)}</span>
              </div>
              <span className="ProductCard__bidCount">
                {bids.length} bid{bids.length !== 1 ? 's' : ''}
              </span>
            </div>
          )}

          {/* Price */}
          <div className="ProductCard__pricing">
            <div className="ProductCard__priceContainer">
              <span className="ProductCard__price">${price.toFixed(2)}</span>
              {hasDiscount && (
                <span className="ProductCard__originalPrice">
                  ${originalPrice.toFixed(2)}
                </span>
              )}
            </div>
            {!showBids && (
              <button className="ProductCard__buyButton">
                View Details
              </button>
            )}
          </div>
        </div>
      </Link>
    </div>
  );
};

export default ProductCard;
