.Navbar {
  background-color: var(--bg-card);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.Navbar__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) 0;
  gap: var(--spacing-lg);
}

.Navbar__logo {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
  text-decoration: none;
  flex-shrink: 0;
}

.Navbar__logoText {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.Navbar__search {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
  max-width: 500px;
}

.Navbar__searchInput {
  position: relative;
  flex: 1;
}

.Navbar__searchInput input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 2.5rem;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.Navbar__searchInput input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.Navbar__searchIcon {
  position: absolute;
  left: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: var(--font-size-lg);
}

.Navbar__searchButton {
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.Navbar__searchButton:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.Navbar__links {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.Navbar__link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.Navbar__link:hover {
  color: var(--primary-color);
  background-color: var(--bg-secondary);
}

.Navbar__actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-shrink: 0;
}

.Navbar__userMenu {
  position: relative;
}

.Navbar__userButton {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: none;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.Navbar__userButton:hover {
  background-color: var(--bg-secondary);
}

.Navbar__userAvatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.Navbar__userName {
  font-weight: 500;
  color: var(--text-primary);
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.Navbar__dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-sm);
  min-width: 180px;
  z-index: 1000;
}

.Navbar__dropdownItem {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-secondary);
  text-decoration: none;
  border: none;
  background: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
}

.Navbar__dropdownItem:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.Navbar__authButtons {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.Navbar__loginButton {
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.Navbar__loginButton:hover {
  color: var(--primary-color);
  background-color: var(--bg-secondary);
}

.Navbar__registerButton {
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--primary-color);
  color: var(--text-white);
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-weight: 500;
  transition: all var(--transition-fast);
}

.Navbar__registerButton:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.Navbar__mobileMenuButton {
  display: none;
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
}

.Navbar__mobileMenu {
  display: none;
  padding: var(--spacing-md) 0;
  border-top: 1px solid var(--border-light);
  background-color: var(--bg-card);
}

.Navbar__mobileSearch {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.Navbar__mobileLinks {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.Navbar__mobileLink {
  padding: var(--spacing-md);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: var(--font-size-base);
}

.Navbar__mobileLink:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .Navbar__search {
    display: none;
  }

  .Navbar__links {
    display: none;
  }

  .Navbar__userName {
    display: none;
  }

  .Navbar__mobileMenuButton {
    display: block;
  }

  .Navbar__mobileMenu {
    display: block;
  }

  .Navbar__authButtons {
    display: none;
  }
}

@media (max-width: 480px) {
  .Navbar__content {
    padding: var(--spacing-sm) 0;
  }

  .Navbar__logo {
    font-size: var(--font-size-lg);
  }
}
