import React, { useState } from 'react';
import { FiX, FiDollarSign } from 'react-icons/fi';
import './BiddingModal.css';

const BiddingModal = ({ product, onClose, onSubmit }) => {
  const [bidAmount, setBidAmount] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const highestBid = product.bids.length > 0 
    ? Math.max(...product.bids.map(bid => bid.amount))
    : 0;

  const minBid = Math.max(highestBid + 1, product.price * 0.8); // Minimum 80% of asking price

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const amount = parseFloat(bidAmount);
    
    if (!amount || amount < minBid) {
      setError(`Bid must be at least $${minBid.toFixed(2)}`);
      return;
    }

    if (amount >= product.price) {
      setError(`Bid must be less than the asking price of $${product.price.toFixed(2)}`);
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      const result = await onSubmit(amount);
      if (result.success) {
        onClose();
      } else {
        setError(result.error || 'Failed to place bid');
      }
    } catch (error) {
      setError('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const suggestedBids = [
    Math.ceil(minBid),
    Math.ceil(minBid + (product.price - minBid) * 0.25),
    Math.ceil(minBid + (product.price - minBid) * 0.5),
    Math.ceil(minBid + (product.price - minBid) * 0.75)
  ].filter(bid => bid < product.price);

  return (
    <div className="BiddingModal" onClick={handleBackdropClick}>
      <div className="BiddingModal__content">
        <div className="BiddingModal__header">
          <h2 className="BiddingModal__title">Make an Offer</h2>
          <button 
            className="BiddingModal__closeButton"
            onClick={onClose}
          >
            <FiX />
          </button>
        </div>

        <div className="BiddingModal__productInfo">
          <img 
            src={product.thumbnail} 
            alt={product.title}
            className="BiddingModal__productImage"
          />
          <div className="BiddingModal__productDetails">
            <h3 className="BiddingModal__productTitle">{product.title}</h3>
            <p className="BiddingModal__productPrice">
              Asking Price: <span>${product.price.toFixed(2)}</span>
            </p>
            {highestBid > 0 && (
              <p className="BiddingModal__highestBid">
                Highest Bid: <span>${highestBid.toFixed(2)}</span>
              </p>
            )}
          </div>
        </div>

        <form className="BiddingModal__form" onSubmit={handleSubmit}>
          <div className="BiddingModal__field">
            <label className="BiddingModal__label">
              <FiDollarSign className="BiddingModal__icon" />
              Your Offer
            </label>
            <input
              type="number"
              step="0.01"
              min={minBid}
              max={product.price - 0.01}
              value={bidAmount}
              onChange={(e) => {
                setBidAmount(e.target.value);
                setError('');
              }}
              className={`BiddingModal__input ${error ? 'BiddingModal__input--error' : ''}`}
              placeholder={`Minimum $${minBid.toFixed(2)}`}
              required
            />
            {error && <span className="BiddingModal__error">{error}</span>}
          </div>

          <div className="BiddingModal__suggestions">
            <p className="BiddingModal__suggestionsLabel">Quick Select:</p>
            <div className="BiddingModal__suggestedBids">
              {suggestedBids.map((amount, index) => (
                <button
                  key={index}
                  type="button"
                  className="BiddingModal__suggestedBid"
                  onClick={() => setBidAmount(amount.toString())}
                >
                  ${amount}
                </button>
              ))}
            </div>
          </div>

          <div className="BiddingModal__info">
            <div className="BiddingModal__infoItem">
              <span className="BiddingModal__infoLabel">Minimum Bid:</span>
              <span className="BiddingModal__infoValue">${minBid.toFixed(2)}</span>
            </div>
            <div className="BiddingModal__infoItem">
              <span className="BiddingModal__infoLabel">Maximum Bid:</span>
              <span className="BiddingModal__infoValue">${(product.price - 0.01).toFixed(2)}</span>
            </div>
          </div>

          <div className="BiddingModal__actions">
            <button
              type="button"
              className="BiddingModal__cancelButton"
              onClick={onClose}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="BiddingModal__submitButton"
              disabled={isSubmitting || !bidAmount}
            >
              {isSubmitting ? 'Placing Bid...' : 'Place Bid'}
            </button>
          </div>
        </form>

        <div className="BiddingModal__disclaimer">
          <p>
            <strong>Note:</strong> Your bid is a commitment to purchase at the offered price 
            if accepted by the seller. Bids cannot be withdrawn once placed.
          </p>
        </div>
      </div>
    </div>
  );
};

export default BiddingModal;
