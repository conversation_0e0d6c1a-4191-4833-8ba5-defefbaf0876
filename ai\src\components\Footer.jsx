import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { FiMail, FiPhone, FiMapPin, FiFacebook, FiTwitter, FiInstagram, FiLinkedin } from 'react-icons/fi';
import './Footer.css';

const Footer = () => {
  return (
    <footer className="Footer">
      <div className="container">
        <div className="Footer__content">
          {/* Company Info */}
          <div className="Footer__section">
            <h3 className="Footer__title">SportsHub</h3>
            <p className="Footer__description">
              Your premier destination for sports training content, eBooks, and workout plans. 
              Connect with expert coaches and elevate your athletic performance.
            </p>
            <div className="Footer__social">
              <a href="#" className="Footer__socialLink" aria-label="Facebook">
                <FiFacebook />
              </a>
              <a href="#" className="Footer__socialLink" aria-label="Twitter">
                <FiTwitter />
              </a>
              <a href="#" className="Footer__socialLink" aria-label="Instagram">
                <FiInstagram />
              </a>
              <a href="#" className="Footer__socialLink" aria-label="LinkedIn">
                <FiLinkedin />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="Footer__section">
            <h4 className="Footer__sectionTitle">Quick Links</h4>
            <ul className="Footer__links">
              <li><Link to="/" className="Footer__link">Browse Content</Link></li>
              <li><Link to="/categories" className="Footer__link">Categories</Link></li>
              <li><Link to="/bidding" className="Footer__link">Bidding</Link></li>
              <li><Link to="/seller-dashboard" className="Footer__link">Become a Seller</Link></li>
              <li><Link to="/about" className="Footer__link">About Us</Link></li>
            </ul>
          </div>

          {/* Categories */}
          <div className="Footer__section">
            <h4 className="Footer__sectionTitle">Categories</h4>
            <ul className="Footer__links">
              <li><Link to="/?category=training-videos" className="Footer__link">Training Videos</Link></li>
              <li><Link to="/?category=ebooks" className="Footer__link">eBooks</Link></li>
              <li><Link to="/?category=workout-plans" className="Footer__link">Workout Plans</Link></li>
              <li><Link to="/?category=nutrition-guides" className="Footer__link">Nutrition Guides</Link></li>
              <li><Link to="/?category=sports-psychology" className="Footer__link">Sports Psychology</Link></li>
            </ul>
          </div>

          {/* Support */}
          <div className="Footer__section">
            <h4 className="Footer__sectionTitle">Support</h4>
            <ul className="Footer__links">
              <li><Link to="/help" className="Footer__link">Help Center</Link></li>
              <li><Link to="/contact" className="Footer__link">Contact Us</Link></li>
              <li><Link to="/faq" className="Footer__link">FAQ</Link></li>
              <li><Link to="/terms" className="Footer__link">Terms of Service</Link></li>
              <li><Link to="/privacy" className="Footer__link">Privacy Policy</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="Footer__section">
            <h4 className="Footer__sectionTitle">Contact</h4>
            <div className="Footer__contact">
              <div className="Footer__contactItem">
                <FiMail className="Footer__contactIcon" />
                <span><EMAIL></span>
              </div>
              <div className="Footer__contactItem">
                <FiPhone className="Footer__contactIcon" />
                <span>+****************</span>
              </div>
              <div className="Footer__contactItem">
                <FiMapPin className="Footer__contactIcon" />
                <span>123 Sports Ave, Athletic City, AC 12345</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="Footer__bottom">
          <div className="Footer__bottomContent">
            <p className="Footer__copyright">
              © 2024 SportsHub. All rights reserved.
            </p>
            <div className="Footer__bottomLinks">
              <Link to="/terms" className="Footer__bottomLink">Terms</Link>
              <Link to="/privacy" className="Footer__bottomLink">Privacy</Link>
              <Link to="/cookies" className="Footer__bottomLink">Cookies</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
