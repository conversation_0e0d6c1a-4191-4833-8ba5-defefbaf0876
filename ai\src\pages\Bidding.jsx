import React, { useState } from 'react';
import { <PERSON><PERSON>ollarSign, FiClock, FiTrendingUp, FiFilter } from 'react-icons/fi';
import { useProducts } from '../context/ProductContext';
import ProductCard from '../components/ProductCard';
import './Bidding.css';

const Bidding = () => {
  const { products } = useProducts();
  const [sortBy, setSortBy] = useState('highest-bid');
  const [filterBy, setFilterBy] = useState('all');

  // Filter products that have bids
  const productsWithBids = products.filter(product => product.bids.length > 0);

  // Apply filters
  let filteredProducts = [...productsWithBids];

  if (filterBy === 'recent') {
    filteredProducts = filteredProducts.filter(product => {
      const latestBid = product.bids[product.bids.length - 1];
      const bidTime = new Date(latestBid.timestamp);
      const now = new Date();
      const hoursDiff = (now - bidTime) / (1000 * 60 * 60);
      return hoursDiff <= 24; // Bids within last 24 hours
    });
  } else if (filterBy === 'high-activity') {
    filteredProducts = filteredProducts.filter(product => product.bids.length >= 3);
  }

  // Apply sorting
  filteredProducts.sort((a, b) => {
    switch (sortBy) {
      case 'highest-bid':
        const highestBidA = Math.max(...a.bids.map(bid => bid.amount));
        const highestBidB = Math.max(...b.bids.map(bid => bid.amount));
        return highestBidB - highestBidA;
      case 'most-bids':
        return b.bids.length - a.bids.length;
      case 'newest':
        const latestBidA = new Date(a.bids[a.bids.length - 1].timestamp);
        const latestBidB = new Date(b.bids[b.bids.length - 1].timestamp);
        return latestBidB - latestBidA;
      case 'ending-soon':
        // In a real app, this would be based on auction end times
        return a.price - b.price; // Placeholder sorting
      default:
        return 0;
    }
  });

  const stats = [
    {
      icon: FiDollarSign,
      label: 'Active Auctions',
      value: productsWithBids.length.toString(),
      color: 'primary'
    },
    {
      icon: FiTrendingUp,
      label: 'Total Bids',
      value: productsWithBids.reduce((sum, product) => sum + product.bids.length, 0).toString(),
      color: 'success'
    },
    {
      icon: FiClock,
      label: 'Recent Activity',
      value: productsWithBids.filter(product => {
        const latestBid = product.bids[product.bids.length - 1];
        const bidTime = new Date(latestBid.timestamp);
        const now = new Date();
        const hoursDiff = (now - bidTime) / (1000 * 60 * 60);
        return hoursDiff <= 24;
      }).length.toString(),
      color: 'warning'
    }
  ];

  return (
    <div className="Bidding">
      <div className="container">
        <div className="Bidding__header">
          <div className="Bidding__headerContent">
            <h1 className="Bidding__title">Bidding & Auctions</h1>
            <p className="Bidding__subtitle">
              Discover great deals and make offers on premium sports content
            </p>
          </div>
        </div>

        {/* Stats */}
        <div className="Bidding__stats">
          {stats.map((stat, index) => (
            <div key={index} className={`Bidding__stat Bidding__stat--${stat.color}`}>
              <div className="Bidding__statIcon">
                <stat.icon />
              </div>
              <div className="Bidding__statContent">
                <span className="Bidding__statValue">{stat.value}</span>
                <span className="Bidding__statLabel">{stat.label}</span>
              </div>
            </div>
          ))}
        </div>

        {/* Filters and Sorting */}
        <div className="Bidding__controls">
          <div className="Bidding__filters">
            <FiFilter className="Bidding__filterIcon" />
            <select 
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value)}
              className="Bidding__filterSelect"
            >
              <option value="all">All Auctions</option>
              <option value="recent">Recent Activity</option>
              <option value="high-activity">High Activity</option>
            </select>
          </div>

          <div className="Bidding__sorting">
            <span className="Bidding__sortLabel">Sort by:</span>
            <select 
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="Bidding__sortSelect"
            >
              <option value="highest-bid">Highest Bid</option>
              <option value="most-bids">Most Bids</option>
              <option value="newest">Latest Activity</option>
              <option value="ending-soon">Ending Soon</option>
            </select>
          </div>
        </div>

        {/* How It Works */}
        <div className="Bidding__howItWorks">
          <h2 className="Bidding__sectionTitle">How Bidding Works</h2>
          <div className="Bidding__steps">
            <div className="Bidding__step">
              <div className="Bidding__stepNumber">1</div>
              <div className="Bidding__stepContent">
                <h3>Browse Products</h3>
                <p>Find content you're interested in and check current bids</p>
              </div>
            </div>
            <div className="Bidding__step">
              <div className="Bidding__stepNumber">2</div>
              <div className="Bidding__stepContent">
                <h3>Make an Offer</h3>
                <p>Place your bid - it must be higher than the current highest bid</p>
              </div>
            </div>
            <div className="Bidding__step">
              <div className="Bidding__stepNumber">3</div>
              <div className="Bidding__stepContent">
                <h3>Wait for Response</h3>
                <p>Sellers can accept your offer or wait for higher bids</p>
              </div>
            </div>
            <div className="Bidding__step">
              <div className="Bidding__stepNumber">4</div>
              <div className="Bidding__stepContent">
                <h3>Complete Purchase</h3>
                <p>If accepted, complete the purchase and access your content</p>
              </div>
            </div>
          </div>
        </div>

        {/* Products with Bids */}
        <div className="Bidding__products">
          <h2 className="Bidding__sectionTitle">
            Active Auctions ({filteredProducts.length})
          </h2>
          
          {filteredProducts.length > 0 ? (
            <div className="Bidding__productsGrid">
              {filteredProducts.map(product => (
                <ProductCard 
                  key={product.id} 
                  product={product} 
                  showBids={true}
                />
              ))}
            </div>
          ) : (
            <div className="Bidding__emptyState">
              <FiDollarSign className="Bidding__emptyIcon" />
              <h3>No active auctions</h3>
              <p>
                {filterBy === 'all' 
                  ? 'There are currently no products with active bids. Check back later!'
                  : 'No auctions match your current filter. Try adjusting your filters.'
                }
              </p>
            </div>
          )}
        </div>

        {/* Bidding Tips */}
        <div className="Bidding__tips">
          <h2 className="Bidding__sectionTitle">Bidding Tips</h2>
          <div className="Bidding__tipsGrid">
            <div className="Bidding__tip">
              <h4>Research First</h4>
              <p>Check the seller's rating and reviews before bidding</p>
            </div>
            <div className="Bidding__tip">
              <h4>Set a Budget</h4>
              <p>Decide your maximum bid amount before you start</p>
            </div>
            <div className="Bidding__tip">
              <h4>Act Quickly</h4>
              <p>Popular items receive multiple bids - don't wait too long</p>
            </div>
            <div className="Bidding__tip">
              <h4>Be Realistic</h4>
              <p>Lowball offers are rarely accepted by sellers</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Bidding;
