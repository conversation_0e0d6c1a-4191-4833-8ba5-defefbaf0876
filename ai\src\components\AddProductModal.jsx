import React, { useState } from 'react';
import { FiX, FiUpload, FiDollarSign, FiFileText } from 'react-icons/fi';
import { categories, sports } from '../data/mockData';
import './AddProductModal.css';

const AddProductModal = ({ onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    originalPrice: '',
    category: 'training-videos',
    sport: 'football',
    type: 'video',
    duration: '',
    pages: '',
    level: 'beginner',
    tags: '',
    thumbnail: '',
    preview: ''
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.price || parseFloat(formData.price) <= 0) {
      newErrors.price = 'Valid price is required';
    }

    if (formData.originalPrice && parseFloat(formData.originalPrice) <= parseFloat(formData.price)) {
      newErrors.originalPrice = 'Original price must be higher than current price';
    }

    if (formData.type === 'video' && !formData.duration.trim()) {
      newErrors.duration = 'Duration is required for videos';
    }

    if (formData.type === 'ebook' && !formData.pages.trim()) {
      newErrors.pages = 'Page count is required for eBooks';
    }

    if (!formData.tags.trim()) {
      newErrors.tags = 'At least one tag is required';
    }

    if (!formData.thumbnail.trim()) {
      newErrors.thumbnail = 'Thumbnail URL is required';
    }

    if (!formData.preview.trim()) {
      newErrors.preview = 'Preview image URL is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const productData = {
        ...formData,
        price: parseFloat(formData.price),
        originalPrice: formData.originalPrice ? parseFloat(formData.originalPrice) : null,
        pages: formData.pages ? parseInt(formData.pages) : null,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
      };

      const result = await onSubmit(productData);
      
      if (!result.success) {
        setErrors({ submit: result.error });
      }
    } catch (error) {
      setErrors({ submit: 'An unexpected error occurred' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const getTypeSpecificFields = () => {
    switch (formData.type) {
      case 'video':
        return (
          <div className="AddProductModal__field">
            <label className="AddProductModal__label">Duration</label>
            <input
              type="text"
              name="duration"
              value={formData.duration}
              onChange={handleInputChange}
              className={`AddProductModal__input ${errors.duration ? 'AddProductModal__input--error' : ''}`}
              placeholder="e.g., 2h 30m"
            />
            {errors.duration && <span className="AddProductModal__error">{errors.duration}</span>}
          </div>
        );
      case 'ebook':
        return (
          <div className="AddProductModal__field">
            <label className="AddProductModal__label">Number of Pages</label>
            <input
              type="number"
              name="pages"
              value={formData.pages}
              onChange={handleInputChange}
              className={`AddProductModal__input ${errors.pages ? 'AddProductModal__input--error' : ''}`}
              placeholder="e.g., 150"
              min="1"
            />
            {errors.pages && <span className="AddProductModal__error">{errors.pages}</span>}
          </div>
        );
      case 'plan':
        return (
          <div className="AddProductModal__field">
            <label className="AddProductModal__label">Plan Duration</label>
            <input
              type="text"
              name="duration"
              value={formData.duration}
              onChange={handleInputChange}
              className={`AddProductModal__input ${errors.duration ? 'AddProductModal__input--error' : ''}`}
              placeholder="e.g., 12 weeks"
            />
            {errors.duration && <span className="AddProductModal__error">{errors.duration}</span>}
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="AddProductModal" onClick={handleBackdropClick}>
      <div className="AddProductModal__content">
        <div className="AddProductModal__header">
          <h2 className="AddProductModal__title">Add New Product</h2>
          <button 
            className="AddProductModal__closeButton"
            onClick={onClose}
          >
            <FiX />
          </button>
        </div>

        <form className="AddProductModal__form" onSubmit={handleSubmit}>
          <div className="AddProductModal__row">
            <div className="AddProductModal__field">
              <label className="AddProductModal__label">Title *</label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className={`AddProductModal__input ${errors.title ? 'AddProductModal__input--error' : ''}`}
                placeholder="Enter product title"
              />
              {errors.title && <span className="AddProductModal__error">{errors.title}</span>}
            </div>
          </div>

          <div className="AddProductModal__field">
            <label className="AddProductModal__label">Description *</label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              className={`AddProductModal__textarea ${errors.description ? 'AddProductModal__input--error' : ''}`}
              placeholder="Describe your product..."
              rows="4"
            />
            {errors.description && <span className="AddProductModal__error">{errors.description}</span>}
          </div>

          <div className="AddProductModal__row">
            <div className="AddProductModal__field">
              <label className="AddProductModal__label">Category *</label>
              <select
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="AddProductModal__select"
              >
                {categories.filter(cat => cat.slug !== 'all').map(category => (
                  <option key={category.id} value={category.slug}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="AddProductModal__field">
              <label className="AddProductModal__label">Sport *</label>
              <select
                name="sport"
                value={formData.sport}
                onChange={handleInputChange}
                className="AddProductModal__select"
              >
                {sports.map(sport => (
                  <option key={sport.id} value={sport.slug}>
                    {sport.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="AddProductModal__row">
            <div className="AddProductModal__field">
              <label className="AddProductModal__label">Type *</label>
              <select
                name="type"
                value={formData.type}
                onChange={handleInputChange}
                className="AddProductModal__select"
              >
                <option value="video">Video</option>
                <option value="ebook">eBook</option>
                <option value="plan">Workout Plan</option>
              </select>
            </div>

            <div className="AddProductModal__field">
              <label className="AddProductModal__label">Level *</label>
              <select
                name="level"
                value={formData.level}
                onChange={handleInputChange}
                className="AddProductModal__select"
              >
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>
          </div>

          <div className="AddProductModal__row">
            {getTypeSpecificFields()}
          </div>

          <div className="AddProductModal__row">
            <div className="AddProductModal__field">
              <label className="AddProductModal__label">Price ($) *</label>
              <input
                type="number"
                step="0.01"
                min="0.01"
                name="price"
                value={formData.price}
                onChange={handleInputChange}
                className={`AddProductModal__input ${errors.price ? 'AddProductModal__input--error' : ''}`}
                placeholder="0.00"
              />
              {errors.price && <span className="AddProductModal__error">{errors.price}</span>}
            </div>

            <div className="AddProductModal__field">
              <label className="AddProductModal__label">Original Price ($)</label>
              <input
                type="number"
                step="0.01"
                min="0.01"
                name="originalPrice"
                value={formData.originalPrice}
                onChange={handleInputChange}
                className={`AddProductModal__input ${errors.originalPrice ? 'AddProductModal__input--error' : ''}`}
                placeholder="Optional - for discounts"
              />
              {errors.originalPrice && <span className="AddProductModal__error">{errors.originalPrice}</span>}
            </div>
          </div>

          <div className="AddProductModal__field">
            <label className="AddProductModal__label">Tags * (comma-separated)</label>
            <input
              type="text"
              name="tags"
              value={formData.tags}
              onChange={handleInputChange}
              className={`AddProductModal__input ${errors.tags ? 'AddProductModal__input--error' : ''}`}
              placeholder="e.g., football, training, advanced, techniques"
            />
            {errors.tags && <span className="AddProductModal__error">{errors.tags}</span>}
          </div>

          <div className="AddProductModal__field">
            <label className="AddProductModal__label">Thumbnail Image URL *</label>
            <input
              type="url"
              name="thumbnail"
              value={formData.thumbnail}
              onChange={handleInputChange}
              className={`AddProductModal__input ${errors.thumbnail ? 'AddProductModal__input--error' : ''}`}
              placeholder="https://example.com/thumbnail.jpg"
            />
            {errors.thumbnail && <span className="AddProductModal__error">{errors.thumbnail}</span>}
          </div>

          <div className="AddProductModal__field">
            <label className="AddProductModal__label">Preview Image URL *</label>
            <input
              type="url"
              name="preview"
              value={formData.preview}
              onChange={handleInputChange}
              className={`AddProductModal__input ${errors.preview ? 'AddProductModal__input--error' : ''}`}
              placeholder="https://example.com/preview.jpg"
            />
            {errors.preview && <span className="AddProductModal__error">{errors.preview}</span>}
          </div>

          {errors.submit && (
            <div className="AddProductModal__submitError">
              {errors.submit}
            </div>
          )}

          <div className="AddProductModal__actions">
            <button
              type="button"
              className="AddProductModal__cancelButton"
              onClick={onClose}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="AddProductModal__submitButton"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Adding Product...' : 'Add Product'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddProductModal;
