import React, { useState, useEffect } from 'react';
import { useProducts } from '../context/ProductContext';
import './PriceFilter.css';

const PriceFilter = () => {
  const { filters, updateFilters } = useProducts();
  const [localRange, setLocalRange] = useState(filters.priceRange);

  useEffect(() => {
    setLocalRange(filters.priceRange);
  }, [filters.priceRange]);

  const handleRangeChange = (index, value) => {
    const newRange = [...localRange];
    newRange[index] = parseInt(value);
    
    // Ensure min doesn't exceed max and vice versa
    if (index === 0 && newRange[0] > newRange[1]) {
      newRange[1] = newRange[0];
    } else if (index === 1 && newRange[1] < newRange[0]) {
      newRange[0] = newRange[1];
    }
    
    setLocalRange(newRange);
  };

  const handleRangeCommit = () => {
    updateFilters({ priceRange: localRange });
  };

  const presetRanges = [
    { label: 'Under $25', range: [0, 25] },
    { label: '$25 - $50', range: [25, 50] },
    { label: '$50 - $100', range: [50, 100] },
    { label: 'Over $100', range: [100, 200] }
  ];

  const handlePresetClick = (range) => {
    setLocalRange(range);
    updateFilters({ priceRange: range });
  };

  return (
    <div className="PriceFilter">
      <h3 className="PriceFilter__title">Price Range</h3>
      
      {/* Custom Range Sliders */}
      <div className="PriceFilter__sliders">
        <div className="PriceFilter__sliderGroup">
          <label className="PriceFilter__label">
            Min: ${localRange[0]}
          </label>
          <input
            type="range"
            min="0"
            max="200"
            step="5"
            value={localRange[0]}
            onChange={(e) => handleRangeChange(0, e.target.value)}
            onMouseUp={handleRangeCommit}
            onTouchEnd={handleRangeCommit}
            className="PriceFilter__slider PriceFilter__slider--min"
          />
        </div>
        
        <div className="PriceFilter__sliderGroup">
          <label className="PriceFilter__label">
            Max: ${localRange[1]}
          </label>
          <input
            type="range"
            min="0"
            max="200"
            step="5"
            value={localRange[1]}
            onChange={(e) => handleRangeChange(1, e.target.value)}
            onMouseUp={handleRangeCommit}
            onTouchEnd={handleRangeCommit}
            className="PriceFilter__slider PriceFilter__slider--max"
          />
        </div>
      </div>

      {/* Range Display */}
      <div className="PriceFilter__display">
        <span className="PriceFilter__range">
          ${localRange[0]} - ${localRange[1]}
        </span>
      </div>

      {/* Preset Ranges */}
      <div className="PriceFilter__presets">
        <h4 className="PriceFilter__presetsTitle">Quick Select</h4>
        <div className="PriceFilter__presetButtons">
          {presetRanges.map((preset, index) => (
            <button
              key={index}
              className={`PriceFilter__presetButton ${
                localRange[0] === preset.range[0] && localRange[1] === preset.range[1]
                  ? 'PriceFilter__presetButton--active'
                  : ''
              }`}
              onClick={() => handlePresetClick(preset.range)}
            >
              {preset.label}
            </button>
          ))}
        </div>
      </div>

      {/* Reset Button */}
      <button
        className="PriceFilter__reset"
        onClick={() => handlePresetClick([0, 200])}
      >
        Reset Price Filter
      </button>
    </div>
  );
};

export default PriceFilter;
