import React from 'react';
import { FiPlay, FiBookOpen, FiFileText, FiHeart, FiBrain } from 'react-icons/fi';
import { useProducts } from '../context/ProductContext';
import './CategoryFilter.css';

const CategoryFilter = () => {
  const { categories, filters, updateFilters } = useProducts();

  const getCategoryIcon = (slug) => {
    switch (slug) {
      case 'training-videos':
        return <FiPlay />;
      case 'ebooks':
        return <FiBookOpen />;
      case 'workout-plans':
        return <FiFileText />;
      case 'nutrition-guides':
        return <FiHeart />;
      case 'sports-psychology':
        return <FiBrain />;
      default:
        return <FiFileText />;
    }
  };

  const handleCategoryChange = (categorySlug) => {
    updateFilters({ category: categorySlug });
  };

  return (
    <div className="CategoryFilter">
      <h3 className="CategoryFilter__title">Categories</h3>
      <div className="CategoryFilter__options">
        {categories.map(category => (
          <button
            key={category.id}
            className={`CategoryFilter__option ${
              filters.category === category.slug ? 'CategoryFilter__option--active' : ''
            }`}
            onClick={() => handleCategoryChange(category.slug)}
          >
            <span className="CategoryFilter__icon">
              {getCategoryIcon(category.slug)}
            </span>
            <span className="CategoryFilter__name">{category.name}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default CategoryFilter;
