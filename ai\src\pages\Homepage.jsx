import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FiSearch, FiTrendingUp, FiStar, FiUsers } from 'react-icons/fi';
import ProductCard from '../components/ProductCard';
import CategoryFilter from '../components/CategoryFilter';
import PriceFilter from '../components/PriceFilter';
import { useProducts } from '../context/ProductContext';
import './Homepage.css';

const Homepage = () => {
  const { 
    filteredProducts, 
    getFeaturedProducts, 
    categories, 
    sports,
    filters,
    updateFilters,
    sortBy,
    setSortBy 
  } = useProducts();
  
  const [searchQuery, setSearchQuery] = useState('');
  const featuredProducts = getFeaturedProducts();

  const handleSearch = (e) => {
    e.preventDefault();
    updateFilters({ searchQuery });
  };

  const stats = [
    { icon: FiUsers, label: 'Expert Coaches', value: '500+' },
    { icon: FiStar, label: 'Premium Content', value: '1000+' },
    { icon: FiTrendingUp, label: 'Success Stories', value: '10k+' }
  ];

  return (
    <div className="Homepage">
      {/* Hero Section */}
      <section className="Homepage__hero">
        <div className="container">
          <div className="Homepage__heroContent">
            <div className="Homepage__heroText">
              <h1 className="Homepage__heroTitle">
                Elevate Your Athletic Performance
              </h1>
              <p className="Homepage__heroSubtitle">
                Access premium training videos, expert eBooks, and personalized workout plans 
                from professional coaches and athletes. Start your journey to excellence today.
              </p>
              
              {/* Hero Search */}
              <form className="Homepage__heroSearch" onSubmit={handleSearch}>
                <div className="Homepage__searchInput">
                  <FiSearch className="Homepage__searchIcon" />
                  <input
                    type="text"
                    placeholder="Search for training videos, eBooks, workout plans..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <button type="submit" className="Homepage__searchButton">
                  Search
                </button>
              </form>

              {/* Popular Searches */}
              <div className="Homepage__popularSearches">
                <span className="Homepage__popularLabel">Popular:</span>
                <div className="Homepage__popularTags">
                  <button 
                    className="Homepage__popularTag"
                    onClick={() => updateFilters({ searchQuery: 'football training' })}
                  >
                    Football Training
                  </button>
                  <button 
                    className="Homepage__popularTag"
                    onClick={() => updateFilters({ searchQuery: 'basketball fundamentals' })}
                  >
                    Basketball
                  </button>
                  <button 
                    className="Homepage__popularTag"
                    onClick={() => updateFilters({ searchQuery: 'nutrition guide' })}
                  >
                    Nutrition
                  </button>
                  <button 
                    className="Homepage__popularTag"
                    onClick={() => updateFilters({ searchQuery: 'mental toughness' })}
                  >
                    Mental Training
                  </button>
                </div>
              </div>
            </div>

            <div className="Homepage__heroImage">
              <img 
                src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop" 
                alt="Athletic training"
                className="Homepage__heroImg"
              />
            </div>
          </div>

          {/* Stats */}
          <div className="Homepage__stats">
            {stats.map((stat, index) => (
              <div key={index} className="Homepage__stat">
                <stat.icon className="Homepage__statIcon" />
                <div className="Homepage__statContent">
                  <span className="Homepage__statValue">{stat.value}</span>
                  <span className="Homepage__statLabel">{stat.label}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="Homepage__featured">
        <div className="container">
          <div className="Homepage__sectionHeader">
            <h2 className="Homepage__sectionTitle">Featured Content</h2>
            <p className="Homepage__sectionSubtitle">
              Hand-picked premium content from our top-rated coaches and experts
            </p>
          </div>
          
          <div className="Homepage__featuredGrid">
            {featuredProducts.slice(0, 6).map(product => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>

          <div className="Homepage__sectionFooter">
            <Link to="/browse" className="Homepage__viewAllButton">
              View All Featured Content
            </Link>
          </div>
        </div>
      </section>

      {/* Browse Section */}
      <section className="Homepage__browse">
        <div className="container">
          <div className="Homepage__sectionHeader">
            <h2 className="Homepage__sectionTitle">Browse All Content</h2>
            <p className="Homepage__sectionSubtitle">
              Filter and discover content that matches your training goals
            </p>
          </div>

          <div className="Homepage__browseContent">
            {/* Filters Sidebar */}
            <div className="Homepage__filters">
              <CategoryFilter />
              <PriceFilter />
              
              {/* Sport Filter */}
              <div className="Homepage__filterSection">
                <h3 className="Homepage__filterTitle">Sport</h3>
                <select 
                  value={filters.sport}
                  onChange={(e) => updateFilters({ sport: e.target.value })}
                  className="Homepage__filterSelect"
                >
                  <option value="all">All Sports</option>
                  {sports.map(sport => (
                    <option key={sport.id} value={sport.slug}>
                      {sport.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Level Filter */}
              <div className="Homepage__filterSection">
                <h3 className="Homepage__filterTitle">Level</h3>
                <select 
                  value={filters.level}
                  onChange={(e) => updateFilters({ level: e.target.value })}
                  className="Homepage__filterSelect"
                >
                  <option value="all">All Levels</option>
                  <option value="beginner">Beginner</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                </select>
              </div>
            </div>

            {/* Products Grid */}
            <div className="Homepage__productsSection">
              {/* Sort Controls */}
              <div className="Homepage__sortControls">
                <span className="Homepage__resultsCount">
                  {filteredProducts.length} results found
                </span>
                <select 
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="Homepage__sortSelect"
                >
                  <option value="featured">Featured</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="rating">Highest Rated</option>
                  <option value="newest">Newest</option>
                </select>
              </div>

              {/* Products Grid */}
              <div className="Homepage__productsGrid">
                {filteredProducts.length > 0 ? (
                  filteredProducts.map(product => (
                    <ProductCard key={product.id} product={product} />
                  ))
                ) : (
                  <div className="Homepage__noResults">
                    <h3>No products found</h3>
                    <p>Try adjusting your filters or search terms</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Homepage;
