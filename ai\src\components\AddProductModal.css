.AddProductModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-md);
}

.AddProductModal__content {
  background-color: var(--bg-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  border: 1px solid var(--border-light);
}

.AddProductModal__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
}

.AddProductModal__title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.AddProductModal__closeButton {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.AddProductModal__closeButton:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.AddProductModal__form {
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.AddProductModal__row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.AddProductModal__field {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.AddProductModal__label {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.AddProductModal__input,
.AddProductModal__textarea,
.AddProductModal__select {
  padding: var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  transition: all var(--transition-fast);
  background-color: var(--bg-card);
}

.AddProductModal__input:focus,
.AddProductModal__textarea:focus,
.AddProductModal__select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.AddProductModal__input--error,
.AddProductModal__textarea--error {
  border-color: var(--danger-color);
}

.AddProductModal__textarea {
  resize: vertical;
  min-height: 100px;
}

.AddProductModal__error {
  font-size: var(--font-size-sm);
  color: var(--danger-color);
}

.AddProductModal__submitError {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  text-align: center;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.AddProductModal__actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.AddProductModal__cancelButton {
  flex: 1;
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.AddProductModal__cancelButton:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.AddProductModal__submitButton {
  flex: 2;
  padding: var(--spacing-md);
  background-color: var(--primary-color);
  border: none;
  border-radius: var(--radius-lg);
  color: var(--text-white);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.AddProductModal__submitButton:hover:not(:disabled) {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.AddProductModal__submitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .AddProductModal {
    padding: var(--spacing-sm);
  }

  .AddProductModal__content {
    max-height: 95vh;
  }

  .AddProductModal__row {
    grid-template-columns: 1fr;
  }

  .AddProductModal__header,
  .AddProductModal__form {
    padding: var(--spacing-lg);
  }

  .AddProductModal__actions {
    flex-direction: column;
  }
}
